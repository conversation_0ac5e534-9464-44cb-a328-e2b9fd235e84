{"include": ["src/**/*"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node", "vite/client"], "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/pages/*": ["src/pages/*"], "@/screens/*": ["src/screens/*"], "@/containers/*": ["src/containers/*"], "@/services/*": ["src/services/*"], "@/hooks/*": ["src/hooks/*"], "@/lib/*": ["src/lib/*"], "@/contexts/*": ["src/contexts/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"]}}}