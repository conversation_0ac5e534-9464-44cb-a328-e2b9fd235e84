# Dependencies
node_modules/
dist/
build/
coverage/

# Generated files
*.min.js
*.bundle.js
*.min.css

# Config files
pnpm-lock.yaml
package-lock.json
yarn.lock

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/

# Public assets
public/assets/
