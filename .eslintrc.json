{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:import/typescript", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "react-hooks", "@typescript-eslint", "jsx-a11y", "import", "prettier"], "rules": {"prettier/prettier": "error", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always"}], "import/no-unresolved": "off", "jsx-a11y/anchor-is-valid": "off"}, "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {"alwaysTryTypes": true}}}}