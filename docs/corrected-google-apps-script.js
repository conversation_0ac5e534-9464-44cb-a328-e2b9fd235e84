/**
 * Corrected Google Apps Script for handling onboarding data
 *
 * This script properly handles the OnboardingState structure from your TypeScript application
 * and maps it to the correct Google Sheets columns.
 */

// The name of the sheet where data will be stored.
const SHEET_NAME = 'OnboardingData';

// Define the complete set of headers for your sheet in the specified snake_case format.
const HEADERS = [
  'full_name',
  'email',
  'password',
  'domain',
  'wp_url',
  'plugin_installed',
  'plugin_verified',
  'wp_username',
  'wp_password',
  'plan_id',
  'plan_name',
  'plan_price',
  'plan_currency',
  'last_updated_at',
  'raw_data', // Add a column for debugging/backup
];

/**
 * This function handles the POST request from your application.
 * It finds a user's row based on their email and updates it, or creates a new row.
 * @param {Object} e - The event parameter for a POST request from your app.
 */
function doPost(e) {
  try {
    // Log the incoming request for debugging
    console.log('Received POST request:', {
      postData: e.postData,
      parameters: e.parameter,
    });

    let sheet =
      SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAME);

    // Initialize sheet with headers if it's new or empty.
    if (!sheet) {
      sheet = SpreadsheetApp.getActiveSpreadsheet().insertSheet(SHEET_NAME);
      sheet.appendRow(HEADERS);
      console.log('Created new sheet with headers');
    } else if (sheet.getLastRow() === 0) {
      sheet.appendRow(HEADERS);
      console.log('Added headers to existing empty sheet');
    }

    // Parse the incoming data
    if (!e.postData || !e.postData.contents) {
      console.error('No postData or contents found');
      return createJsonResponse({
        result: 'error',
        message: 'No data received in request',
      });
    }

    const data = JSON.parse(e.postData.contents);
    console.log('Parsed data:', data);

    // Extract email from the auth object
    const email = data.auth?.email;

    // An email is required to uniquely identify and save a user's progress.
    if (!email) {
      console.error('No email found in data:', data);
      return createJsonResponse({
        result: 'error',
        message: 'Email is missing from the data.',
      });
    }

    const dataRange = sheet.getDataRange();
    const values = dataRange.getValues();
    const emailColumnIndex = HEADERS.indexOf('email');
    let rowIndex = -1;

    // Find the row for the given email. Start from row 1 to skip the header.
    for (let i = 1; i < values.length; i++) {
      if (values[i][emailColumnIndex] === email) {
        rowIndex = i;
        break;
      }
    }

    // Construct the full row of data, mapping from the camelCase JSON to the snake_case columns.
    const rowData = [
      data.auth?.fullName || '', // full_name
      email, // email
      data.auth?.password || '', // password (consider security implications)
      data.website?.domain || '', // domain
      data.website?.wordpressUrl || '', // wp_url
      data.plugin?.pluginInstalled || false, // plugin_installed
      data.plugin?.isVerified || false, // plugin_verified
      data.credentials?.username || '', // wp_username
      data.credentials?.password || '', // wp_password (consider security implications)
      data.plan?.planId || '', // plan_id
      data.plan?.planName || '', // plan_name
      data.plan?.price || '', // plan_price
      data.plan?.currency || '', // plan_currency
      new Date(), // last_updated_at
      JSON.stringify(data), // raw_data for debugging
    ];

    console.log('Constructed row data:', rowData);

    if (rowIndex !== -1) {
      // An existing row was found, so update it.
      sheet.getRange(rowIndex + 1, 1, 1, rowData.length).setValues([rowData]);
      console.log('Updated existing row:', rowIndex + 1);
    } else {
      // No row was found for this email, so append a new one.
      sheet.appendRow(rowData);
      console.log('Added new row for email:', email);
    }

    return createJsonResponse({
      result: 'success',
      message: 'Onboarding data saved successfully.',
      email: email,
      action: rowIndex !== -1 ? 'updated' : 'created',
    });
  } catch (error) {
    console.error('Error in doPost:', error.toString(), error.stack);
    return createJsonResponse({
      result: 'error',
      message: `An error occurred: ${error.toString()}`,
      stack: error.stack,
    });
  }
}

/**
 * Helper function to create a standardized JSON response.
 * @param {Object} responseObject - The object to be stringified.
 * @returns {ContentService.TextOutput} - The JSON response object.
 */
function createJsonResponse(responseObject) {
  const response = ContentService.createTextOutput(
    JSON.stringify(responseObject)
  ).setMimeType(ContentService.MimeType.JSON);

  // Add CORS headers to allow requests from your domain
  response.setHeaders({
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  });

  return response;
}

/**
 * Handle preflight OPTIONS requests for CORS
 */
function doOptions() {
  return createJsonResponse({ message: 'CORS preflight' });
}

/**
 * Test function to verify the script is working
 * You can call this from the Apps Script editor to test
 */
function testScript() {
  const testData = {
    auth: {
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'testpass',
    },
    website: {
      domain: 'example.com',
      wordpressUrl: 'https://example.com/wp-admin',
    },
    plugin: {
      pluginInstalled: true,
      isVerified: true,
    },
    credentials: {
      username: 'wpuser',
      password: 'wppass',
    },
    plan: {
      planId: 'basic',
      planName: 'Basic Plan',
      price: 29.99,
      currency: 'USD',
    },
  };

  const mockEvent = {
    postData: {
      contents: JSON.stringify(testData),
    },
  };

  const result = doPost(mockEvent);
  console.log('Test result:', result.getContent());
}
