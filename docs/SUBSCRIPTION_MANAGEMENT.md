# Subscription Management

This module provides comprehensive subscription management functionality using Supabase as the backend.

## Features

- **Real Database Integration**: Uses Supabase tables for plans, subscriptions, and user data
- **Modern UI**: Beautiful, responsive interface with proper loading states
- **Plan Management**: Support for multiple pricing tiers with features
- **Subscription Lifecycle**: Create, cancel, and reactivate subscriptions
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Type Safety**: Full TypeScript support with Supabase generated types

## Database Schema

The subscription system uses these Supabase tables:

### Plans Table

- `id`: Unique plan identifier
- `name`: Plan name (e.g., "Professional")
- `description`: Plan description
- `price`: Base price in cents
- `discounted_price`: Optional discounted price
- `interval`: Billing interval ("month" or "year")
- `features`: JSON array of plan features
- `is_active`: Whether the plan is available for subscription

### Subscriptions Table

- `id`: Unique subscription identifier
- `user_id`: Reference to user (profiles table)
- `plan_id`: Reference to plan
- `status`: Subscription status ("active", "canceled", etc.)
- `current_period_start`: Current billing period start
- `current_period_end`: Current billing period end
- `cancel_at_period_end`: Whether to cancel at period end
- `trial_start` / `trial_end`: Trial period dates

### Websites Table

- `id`: Unique website identifier
- `user_id`: Reference to user
- `domain_name`: Website domain
- `website_url`: Full website URL
- `status`: Website status
- Other website-related fields

## Usage

### Basic Subscription Page

```tsx
import ImprovedSubscriptionManagement from '@/components/dashboard/improved-subscription-management';

export default function SubscriptionsPage() {
  return <ImprovedSubscriptionManagement />;
}
```

### Admin Plan Management

```tsx
import AdminPlansPage from '@/pages/admin/plans/admin-plans-page';

// Use this page to seed initial plans in your database
```

### Services

The subscription system provides these services:

- `getAllPlans()`: Get all available plans
- `getUserSubscriptionWithPlan(userId)`: Get user's active subscription with plan details
- `getWebsitesWithSubscriptions(userId)`: Get user's websites with subscription data
- `createSubscription(data)`: Create a new subscription
- `cancelSubscription(id)`: Cancel a subscription
- `reactivateSubscription(id)`: Reactivate a canceled subscription

## Setup

1. **Database Setup**: Ensure your Supabase database has the required tables (plans, subscriptions, profiles, websites)

2. **Seed Plans**: Use the admin interface or run the seed function:

   ```tsx
   import { seedPlans, seedAnnualPlans } from '@/utils/seed-plans';

   await seedPlans();
   await seedAnnualPlans();
   ```

3. **Authentication**: Ensure users are authenticated before accessing subscription features

## Key Components

- **ImprovedSubscriptionManagement**: Main subscription interface
- **PlanSeeder**: Admin utility for seeding plans
- **AdminPlansPage**: Admin interface for plan management

## Error Handling

The system includes comprehensive error handling:

- Network errors are caught and displayed to users
- Loading states prevent multiple simultaneous actions
- Validation ensures data integrity
- Fallback states for empty data

## Future Enhancements

- Payment provider integration (Stripe, Paddle)
- Webhook handling for payment events
- Usage tracking and billing
- Coupon and discount system
- Team/organization subscriptions
