import { Database } from './generated-types';

export type Website = Database['public']['Tables']['websites']['Row'];
export type InsertWebsite = Database['public']['Tables']['websites']['Insert'];
export type UpdateWebsite = Database['public']['Tables']['websites']['Update'];

// Extended Profile type with apps_script_url field
export type Profile = Database['public']['Tables']['profiles']['Row'] & {
  apps_script_url?: string | null;
};
export type InsertProfile =
  Database['public']['Tables']['profiles']['Insert'] & {
    apps_script_url?: string | null;
  };
export type UpdateProfile =
  Database['public']['Tables']['profiles']['Update'] & {
    apps_script_url?: string | null;
  };

export type Coupon = Database['public']['Tables']['coupons']['Row'];
export type InsertCoupon = Database['public']['Tables']['coupons']['Insert'];
export type UpdateCoupon = Database['public']['Tables']['coupons']['Update'];

export type Plan = Database['public']['Tables']['plans']['Row'];
export type InsertPlan = Database['public']['Tables']['plans']['Insert'];
export type UpdatePlan = Database['public']['Tables']['plans']['Update'];

export type Subscription = Database['public']['Tables']['subscriptions']['Row'];
export type InsertSubscription =
  Database['public']['Tables']['subscriptions']['Insert'];
export type UpdateSubscription =
  Database['public']['Tables']['subscriptions']['Update'];

export type Transaction = Database['public']['Tables']['transactions']['Row'];
export type InsertTransaction =
  Database['public']['Tables']['transactions']['Insert'];
export type UpdateTransaction =
  Database['public']['Tables']['transactions']['Update'];
