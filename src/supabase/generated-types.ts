export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      coupons: {
        Row: {
          code: string;
          created_at: string | null;
          current_uses: number | null;
          description: string | null;
          discount_type: string;
          discount_value: number;
          id: number;
          is_active: boolean | null;
          max_uses: number | null;
          updated_at: string | null;
          valid_from: string | null;
          valid_until: string | null;
        };
        Insert: {
          code: string;
          created_at?: string | null;
          current_uses?: number | null;
          description?: string | null;
          discount_type: string;
          discount_value: number;
          id?: never;
          is_active?: boolean | null;
          max_uses?: number | null;
          updated_at?: string | null;
          valid_from?: string | null;
          valid_until?: string | null;
        };
        Update: {
          code?: string;
          created_at?: string | null;
          current_uses?: number | null;
          description?: string | null;
          discount_type?: string;
          discount_value?: number;
          id?: never;
          is_active?: boolean | null;
          max_uses?: number | null;
          updated_at?: string | null;
          valid_from?: string | null;
          valid_until?: string | null;
        };
        Relationships: [];
      };
      plans: {
        Row: {
          created_at: string | null;
          description: string | null;
          discounted_price: number | null;
          features: Json | null;
          id: number;
          interval: string;
          is_active: boolean | null;
          name: string;
          price: number;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          description?: string | null;
          discounted_price?: number | null;
          features?: Json | null;
          id?: never;
          interval: string;
          is_active?: boolean | null;
          name: string;
          price: number;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          description?: string | null;
          discounted_price?: number | null;
          features?: Json | null;
          id?: never;
          interval?: string;
          is_active?: boolean | null;
          name?: string;
          price?: number;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          email: string | null;
          full_name: string | null;
          id: string;
          role: string | null;
          updated_at: string | null;
          username: string | null;
          website: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id: string;
          role?: string | null;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id?: string;
          role?: string | null;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
        };
        Relationships: [];
      };
      subscription_coupons: {
        Row: {
          applied_at: string | null;
          coupon_id: number;
          created_at: string | null;
          id: number;
          subscription_id: number;
        };
        Insert: {
          applied_at?: string | null;
          coupon_id: number;
          created_at?: string | null;
          id?: never;
          subscription_id: number;
        };
        Update: {
          applied_at?: string | null;
          coupon_id?: number;
          created_at?: string | null;
          id?: never;
          subscription_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'subscription_coupons_coupon_id_fkey';
            columns: ['coupon_id'];
            isOneToOne: false;
            referencedRelation: 'coupons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'subscription_coupons_subscription_id_fkey';
            columns: ['subscription_id'];
            isOneToOne: false;
            referencedRelation: 'subscriptions';
            referencedColumns: ['id'];
          },
        ];
      };
      subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null;
          canceled_at: string | null;
          created_at: string | null;
          current_period_end: string;
          current_period_start: string;
          id: number;
          plan_id: number;
          status: string;
          trial_end: string | null;
          trial_start: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          cancel_at_period_end?: boolean | null;
          canceled_at?: string | null;
          created_at?: string | null;
          current_period_end: string;
          current_period_start: string;
          id?: never;
          plan_id: number;
          status: string;
          trial_end?: string | null;
          trial_start?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          cancel_at_period_end?: boolean | null;
          canceled_at?: string | null;
          created_at?: string | null;
          current_period_end?: string;
          current_period_start?: string;
          id?: never;
          plan_id?: number;
          status?: string;
          trial_end?: string | null;
          trial_start?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'subscriptions_plan_id_fkey';
            columns: ['plan_id'];
            isOneToOne: false;
            referencedRelation: 'plans';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'subscriptions_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      transactions: {
        Row: {
          amount: number;
          coupon_id: number | null;
          created_at: string | null;
          currency: string;
          id: number;
          invoice_url: string | null;
          metadata: Json | null;
          payment_id: string | null;
          payment_method: string | null;
          payment_provider: string | null;
          status: string;
          subscription_id: number | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          amount: number;
          coupon_id?: number | null;
          created_at?: string | null;
          currency?: string;
          id?: never;
          invoice_url?: string | null;
          metadata?: Json | null;
          payment_id?: string | null;
          payment_method?: string | null;
          payment_provider?: string | null;
          status: string;
          subscription_id?: number | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          amount?: number;
          coupon_id?: number | null;
          created_at?: string | null;
          currency?: string;
          id?: never;
          invoice_url?: string | null;
          metadata?: Json | null;
          payment_id?: string | null;
          payment_method?: string | null;
          payment_provider?: string | null;
          status?: string;
          subscription_id?: number | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'transactions_coupon_id_fkey';
            columns: ['coupon_id'];
            isOneToOne: false;
            referencedRelation: 'coupons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'transactions_subscription_id_fkey';
            columns: ['subscription_id'];
            isOneToOne: false;
            referencedRelation: 'subscriptions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'transactions_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      websites: {
        Row: {
          analytics_id: string | null;
          created_at: string | null;
          domain_name: string;
          hosting_credentials: Json | null;
          hosting_provider: string | null;
          id: number;
          ssl_enabled: boolean | null;
          ssl_expiry_date: string | null;
          status: string | null;
          updated_at: string | null;
          user_id: string;
          website_description: string | null;
          website_images: string[] | null;
          website_niche: string | null;
          website_url: string;
          wordpress_id: string | null;
          wordpress_pass: string | null;
        };
        Insert: {
          analytics_id?: string | null;
          created_at?: string | null;
          domain_name: string;
          hosting_credentials?: Json | null;
          hosting_provider?: string | null;
          id?: never;
          ssl_enabled?: boolean | null;
          ssl_expiry_date?: string | null;
          status?: string | null;
          updated_at?: string | null;
          user_id: string;
          website_description?: string | null;
          website_images?: string[] | null;
          website_niche?: string | null;
          website_url: string;
          wordpress_id?: string | null;
          wordpress_pass?: string | null;
        };
        Update: {
          analytics_id?: string | null;
          created_at?: string | null;
          domain_name?: string;
          hosting_credentials?: Json | null;
          hosting_provider?: string | null;
          id?: never;
          ssl_enabled?: boolean | null;
          ssl_expiry_date?: string | null;
          status?: string | null;
          updated_at?: string | null;
          user_id?: string;
          website_description?: string | null;
          website_images?: string[] | null;
          website_niche?: string | null;
          website_url?: string;
          wordpress_id?: string | null;
          wordpress_pass?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'websites_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const;
