import { createClient } from '@supabase/supabase-js';

const supabaseUrl =
  import.meta.env.VITE_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey =
  import.meta.env.VITE_SUPABASE_ANON_KEY || 'placeholder-key';

// For demo purposes, we'll create the client even with placeholder values
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Check if we have valid Supabase configuration
export const hasValidSupabaseConfig = Boolean(
  import.meta.env.VITE_SUPABASE_URL &&
    import.meta.env.VITE_SUPABASE_ANON_KEY &&
    import.meta.env.VITE_SUPABASE_URL !== 'https://placeholder.supabase.co'
);

// Database types
export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Website {
  id: string;
  user_id: string;
  domain: string;
  name: string;
  status: 'active' | 'inactive' | 'pending';
  subscription_status: 'active' | 'inactive' | 'trial' | 'cancelled';
  subscription_plan: 'basic' | 'pro' | 'enterprise';
  created_at: string;
  updated_at: string;
}

export interface Article {
  id: string;
  website_id: string;
  title: string;
  content: string;
  status: 'draft' | 'published' | 'scheduled';
  seo_score?: number;
  keywords?: string[];
  published_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Analytics {
  id: string;
  website_id: string;
  date: string;
  page_views: number;
  unique_visitors: number;
  bounce_rate: number;
  avg_session_duration: number;
  organic_traffic: number;
  created_at: string;
}
