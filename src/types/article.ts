// Article types for Google Apps Script API
export interface Article {
  id: string;
  title: string;
  content?: string;
  excerpt?: string;
  status: 'draft' | 'published' | 'scheduled';
  published_at?: string;
  created_at: string;
  updated_at: string;
  seo_score?: number;
  views?: number;
  author?: string;
  tags?: string[];
  featured_image?: string;
  website_id: string;
}

export interface ArticleStats {
  total_articles: number;
  published_articles: number;
  draft_articles: number;
  total_views: number;
  avg_seo_score: number;
}

export interface FetchArticlesParams {
  website_id?: string;
  status?: string;
  limit?: number;
  offset?: number;
}

export interface CreateArticleParams {
  title: string;
  content: string;
  status: 'draft' | 'published' | 'scheduled';
  website_id: string;
  published_at?: string;
  tags?: string[];
  featured_image?: string;
}

export interface UpdateArticleParams {
  title?: string;
  content?: string;
  status?: 'draft' | 'published' | 'scheduled';
  published_at?: string;
  tags?: string[];
  featured_image?: string;
}
