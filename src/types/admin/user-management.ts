import { Plan, Profile, Subscription } from '@/supabase/types';

// Combined user data with auth info and profile
export interface UserWithAuthData extends Profile {
  auth_user?: {
    id: string;
    email: string;
    email_confirmed_at: string | null;
    last_sign_in_at: string | null;
    created_at: string;
    updated_at: string;
    user_metadata: Record<string, unknown>;
  };
  subscriptions?: (Subscription & { plans: Plan })[];
  subscription_count?: number;
  last_login?: string | null;
}

// User creation data with auth details
export interface CreateUserData {
  // Profile data
  full_name: string;
  email: string;
  username?: string;
  role?: string;
  website?: string;
  avatar_url?: string;

  // Auth data
  password?: string;
  send_email_confirmation?: boolean;
}

// User update data (profile only)
export interface UpdateUserData {
  full_name?: string;
  email?: string;
  username?: string;
  role?: string;
  website?: string;
  avatar_url?: string;
}

// User filters and sorting
export interface UserFilters {
  role?: string;
  status?: 'active' | 'inactive';
  has_subscription?: boolean;
  search?: string;
}

export interface UserSort {
  field: 'created_at' | 'full_name' | 'email' | 'last_login';
  direction: 'asc' | 'desc';
}
