// Define export interfaces for type safety
export interface Stat {
  title: string;
  value: number;
  href: string;
  icon: JSX.Element;
  color: string;
  description: string;
}

export interface QuickAction {
  title: string;
  href: string;
  icon: JSX.Element;
  description?: string;
  color?: string;
  variant?: 'blue' | 'green' | 'purple' | 'orange' | 'default';
}

export interface SystemInfo {
  label: string;
  value: string;
  icon?: JSX.Element;
  status?: 'success' | 'warning' | 'error' | 'info';
  color?: string;
  variant?: 'green' | 'blue' | 'purple' | 'emerald' | 'yellow' | 'red';
}

export interface StatsState {
  users: number;
  subscriptions: number;
  coupons: number;
  plans: number;
  websites: number;
  transactions: number;
}
