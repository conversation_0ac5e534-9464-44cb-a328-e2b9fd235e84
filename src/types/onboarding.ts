export type OnboardingStep =
  | 'auth'
  | 'website'
  | 'plugin'
  | 'credentials'
  | 'plan'
  | 'review';

export interface OnboardingStepProps<T = unknown> {
  onNext: () => void;
  onUpdate: (data: T) => void;
}

export interface WebsiteStepData {
  domain: string;
  wordpressUrl?: string;
}

export interface DomainStepData {
  domain: string;
  wordpressUrl?: string;
}

export interface PluginStepData {
  isVerified: boolean;
  pluginInstalled: boolean;
}

export interface AuthStepData {
  email: string;
  password: string;
  fullName?: string;
}

export interface CredentialsStepData {
  username: string;
  password: string;
  apiKey?: string;
}

export interface PlanStepData {
  planId: string;
  planName: string;
  price: number;
  currency: string;
}

export interface OnboardingState {
  auth?: AuthStepData;
  website?: WebsiteStepData;
  plugin?: PluginStepData;
  credentials?: CredentialsStepData;
  plan?: PlanStepData;
}

export interface StepValidation {
  isValid: boolean;
  errors: string[];
}

export interface OnboardingStepConfig {
  id: OnboardingStep;
  title: string;
  description: string;
  isCompleted: (state: OnboardingState) => boolean;
  validate: (state: OnboardingState) => StepValidation;
}
