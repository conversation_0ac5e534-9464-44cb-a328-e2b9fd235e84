// Database types that match Supabase schema
export interface Profile {
  id: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Website {
  id: string;
  user_id: string;
  domain: string;
  name: string;
  status: 'active' | 'inactive' | 'pending';
  subscription_status: 'active' | 'inactive' | 'trial' | 'cancelled';
  subscription_plan: 'basic' | 'pro' | 'enterprise';
  wordpress_url?: string;
  wordpress_username?: string;
  wordpress_password?: string;
  monthly_views?: number;
  seo_score?: number;
  articles_count?: number;
  created_at: string;
  updated_at: string;
}

export interface Article {
  id: string;
  website_id: string;
  title: string;
  content?: string;
  status: 'draft' | 'published' | 'scheduled';
  seo_score?: number;
  keywords?: string[];
  published_at?: string;
  created_at: string;
  updated_at: string;
}

export interface MediaPlan {
  id: string;
  website_id: string;
  start_date: string;
  end_date: string;
  frequency: 'daily' | 'weekly' | 'monthly';
  budget_amount?: number;
  budget_currency?: string;
  created_at: string;
  updated_at: string;
}

// Insert types for forms
export type InsertWebsite = Omit<Website, 'id' | 'created_at' | 'updated_at'>;
export type InsertArticle = Omit<Article, 'id' | 'created_at' | 'updated_at'>;
export type InsertMediaPlan = Omit<
  MediaPlan,
  'id' | 'created_at' | 'updated_at'
>;

// Onboarding types
export type OnboardingStep =
  | 'domain'
  | 'plugin'
  | 'credentials'
  | 'period'
  | 'budget';

export interface OnboardingData {
  domain?: string;
  pluginVerified?: boolean;
  credentials?: {
    username: string;
    password: string;
  };
  mediaPlan?: {
    articlesPerWeek: number;
    contentStyle: 'informational' | 'promotional' | 'mixed';
    targetAudience: string;
    keywords: string[];
    frequency?: 'daily' | 'weekly' | 'monthly';
    startDate?: Date;
    endDate?: Date;
  };
  budget?: {
    amount: number;
    currency: string;
  };
}

// Admin types
export * from './admin';
