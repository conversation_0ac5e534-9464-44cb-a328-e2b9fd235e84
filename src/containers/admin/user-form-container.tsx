import UserForm from '@/components/admin/profiles/user-form';
import { CardWrapper, SectionWrapper } from '@/components/common';
import { InsertProfile, Profile, UpdateProfile } from '@/supabase/types';
import React from 'react';

interface UserFormContainerProps {
  user?: Profile;
  onSave: (userData: InsertProfile | UpdateProfile) => Promise<void>;
  onCancel: () => void;
}

export const UserFormContainer: React.FC<UserFormContainerProps> = ({
  user,
  onSave,
  onCancel,
}) => {
  return (
    <SectionWrapper spacing="md">
      <CardWrapper
        title={user ? 'Edit User' : 'Add New User'}
        description={
          user ? 'Update user information below.' : 'Create a new user account.'
        }
      >
        <UserForm user={user} onSave={onSave} onCancel={onCancel} />
      </CardWrapper>
    </SectionWrapper>
  );
};
