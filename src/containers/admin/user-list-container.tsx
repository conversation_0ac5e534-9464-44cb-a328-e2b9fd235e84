import UserTable from '@/components/admin/profiles/user-table';
import { Card<PERSON>rapper, SectionWrapper } from '@/components/common';
import { Profile } from '@/supabase/types';
import React from 'react';

interface UserListContainerProps {
  users: Profile[];
  onEdit: (user: Profile) => void;
  onDelete: (id: string) => void;
}

export const UserListContainer: React.FC<UserListContainerProps> = ({
  users,
  onEdit,
  onDelete,
}) => {
  return (
    <SectionWrapper spacing="md">
      <CardWrapper
        title="Users List"
        description={`${users.length} user${users.length !== 1 ? 's' : ''} found`}
      >
        <UserTable users={users} onEdit={onEdit} onDelete={onDelete} />
      </CardWrapper>
    </SectionWrapper>
  );
};
