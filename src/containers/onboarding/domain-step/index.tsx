import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent } from '@/components/ui/card';
import { domainFormSchema } from '@/utils/schemas';
import { ArrowRightIcon } from 'lucide-react';

type Step1DomainProps = {
  onNext: () => void;
  initialValue?: string;
  onUpdate: (domain: string) => void;
};

type FormValues = z.infer<typeof domainFormSchema>;

export default function DomainStep({
  onNext,
  initialValue,
  onUpdate,
}: Step1DomainProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(domainFormSchema),
    defaultValues: {
      domain: initialValue || '',
    },
  });

  const onSubmit = (values: FormValues) => {
    onUpdate(values.domain);
    onNext();
  };

  return (
    <Card className="bg-white rounded-lg shadow-sm">
      <CardContent className="p-6 md:p-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-gray-900">
                What's your website?
              </h2>
              <p className="text-gray-600">
                Tell us about your website domain so we can help you get
                started.
              </p>
            </div>

            <FormField
              control={form.control}
              name="domain"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">
                    Website Domain
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="example.com"
                      className="mt-1"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="pt-4">
              <Button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!form.formState.isValid}
              >
                Continue
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Button>
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500">
                We'll use this to help configure your WordPress integration and
                suggest content topics relevant to your niche.
              </p>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
