import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckIcon, DownloadIcon, ArrowRightIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

type Step2PluginProps = {
  onNext: () => void;
  domain: string;
  isVerified?: boolean;
  onUpdate: (verified: boolean) => void;
};

export default function PluginStep({
  onNext,
  domain,
  isVerified = false,
  onUpdate,
}: Step2PluginProps) {
  const { toast } = useToast();
  const [downloadStarted, setDownloadStarted] = useState(false);

  // Modified to bypass actual plugin registration for testing purposes
  const registerPluginMutation = useMutation({
    mutationFn: async () => {
      // Simulate successful plugin registration
      return Promise.resolve({
        success: true,
        id: 1,
        websiteId: 1,
        verified: false,
      });
    },
  });

  // Modified to bypass actual verification for testing purposes
  const verifyPluginMutation = useMutation({
    mutationFn: async () => {
      // Simulate successful verification
      return Promise.resolve({ success: true, verified: true });
    },
    onSuccess: () => {
      onUpdate(true);
      toast({
        title: 'Plugin verified successfully',
        description: "Let's continue with the next step",
      });
      onNext();
    },
    onError: (error: any) => {
      toast({
        title: 'Verification failed',
        description:
          error.message ||
          'Could not verify plugin installation. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleDownloadPlugin = () => {
    // Register the plugin install in database
    registerPluginMutation.mutate();

    // In a real app, this would trigger a download of the plugin ZIP file
    // For now, we'll just simulate the download and set the state
    setDownloadStarted(true);

    toast({
      title: 'Download started',
      description: 'The plugin will be downloaded to your computer shortly.',
    });
  };

  const handleVerifyConnection = () => {
    verifyPluginMutation.mutate();
  };

  return (
    <Card className="bg-white rounded-lg shadow-sm">
      <CardContent className="p-6 md:p-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Base Setup</h1>
        <p className="text-gray-600 mb-8">
          Let's connect SEO45 AI with your WordPress Website
        </p>

        <div className="space-y-8">
          <div className="relative">
            <div className="absolute left-0 flex items-center justify-center">
              <div className="rounded-full flex items-center justify-center w-10 h-10 bg-primary text-white">
                <CheckIcon className="h-5 w-5" />
              </div>
            </div>
            <div className="ml-16">
              <h3 className="text-lg font-medium text-gray-400">
                Enter your website domain
              </h3>
              <p className="text-sm text-gray-400">
                Example: yourdomain.com or www.yourdomain.com
              </p>
            </div>
          </div>

          <div>
            <div className="flex items-center">
              <div
                className={`rounded-full flex items-center justify-center w-10 h-10 ${
                  isVerified
                    ? 'bg-primary text-white'
                    : 'border-2 border-primary'
                }`}
              >
                {isVerified ? (
                  <CheckIcon className="h-5 w-5" />
                ) : (
                  <ArrowRightIcon className="h-5 w-5 text-primary" />
                )}
              </div>
              <div className="ml-6 flex-grow">
                <h3 className="text-lg font-medium text-gray-900">
                  Download SEO45 plugin, Install and activate in your wordpress.
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  After downloading the plugin, visit your wordpress dashboard{' '}
                  {'->'} Plugins {'->'} Add new Plugin {'->'} Upload {'->'}{' '}
                  Upload SEO45.zip file and activate the plugin.
                </p>

                <div className="mt-4 flex space-x-4">
                  <Button
                    variant="outline"
                    className="bg-gray-900 hover:bg-black text-white"
                    onClick={handleDownloadPlugin}
                    disabled={
                      downloadStarted ||
                      isVerified ||
                      registerPluginMutation.isPending
                    }
                  >
                    <DownloadIcon className="mr-2 h-4 w-4" />
                    {registerPluginMutation.isPending
                      ? 'Processing...'
                      : 'Download Plugin'}
                  </Button>
                  <Button
                    onClick={handleVerifyConnection}
                    disabled={
                      !downloadStarted ||
                      isVerified ||
                      verifyPluginMutation.isPending
                    }
                  >
                    {verifyPluginMutation.isPending
                      ? 'Verifying...'
                      : 'Verify Connection'}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="relative opacity-50">
            <div className="absolute left-0 flex items-center justify-center">
              <div className="rounded-full flex items-center justify-center w-10 h-10 bg-gray-200 text-gray-400">
                3
              </div>
            </div>
            <div className="ml-16">
              <h3 className="text-lg font-medium text-gray-400">
                Choose running period for Media Plan
              </h3>
              <p className="text-sm text-gray-400">
                Add parameters that will be used for your content creation
                schedule
              </p>
            </div>
          </div>

          <div className="relative opacity-50">
            <div className="absolute left-0 flex items-center justify-center">
              <div className="rounded-full flex items-center justify-center w-10 h-10 bg-gray-200 text-gray-400">
                4
              </div>
            </div>
            <div className="ml-16">
              <h3 className="text-lg font-medium text-gray-400">
                Define a Budget
              </h3>
              <p className="text-sm text-gray-400">
                Add parameters that will be used for your content strategy
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
