import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, CheckIcon } from 'lucide-react';

type BudgetStepProps = {
  onNext: () => void;
  initialValues?: {
    amount?: number;
    currency?: string;
  };
  onUpdate: (data: { amount: number; currency: string }) => void;
};

const budgetSchema = z.object({
  budget_amount: z.number().min(1, 'Budget amount must be at least $1'),
  budget_currency: z.string().default('USD'),
});

type FormValues = z.infer<typeof budgetSchema>;

export default function BudgetStep({
  onNext,
  initialValues,
  onUpdate,
}: BudgetStepProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(budgetSchema),
    defaultValues: {
      budget_amount: initialValues?.amount || 100,
      budget_currency: initialValues?.currency || 'USD',
    },
  });

  const onSubmit = (values: FormValues) => {
    onUpdate({
      amount: values.budget_amount,
      currency: values.budget_currency,
    });
    onNext();
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="bg-white shadow-lg border-0">
        <CardHeader className="text-center pb-4">
          <div className="mx-auto mb-4 w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
            <DollarSign className="w-8 h-8 text-purple-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Set Your Budget
          </CardTitle>
          <p className="text-gray-600 mt-2">
            Define your monthly content budget to get started.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-1">
                  <FormField
                    control={form.control}
                    name="budget_currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                            <SelectItem value="CAD">CAD</SelectItem>
                            <SelectItem value="AUD">AUD</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="col-span-2">
                  <FormField
                    control={form.control}
                    name="budget_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Monthly Budget</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            step="1"
                            placeholder="100"
                            {...field}
                            onChange={e =>
                              field.onChange(parseInt(e.target.value) || 0)
                            }
                            className="h-12 text-lg"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">
                  What's included:
                </h4>
                <ul className="text-sm text-purple-700 space-y-1">
                  <li>✓ AI-generated content articles</li>
                  <li>✓ SEO optimization and keyword research</li>
                  <li>✓ Automated publishing to WordPress</li>
                  <li>✓ Performance analytics and reporting</li>
                  <li>✓ Content strategy recommendations</li>
                </ul>
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-purple-600 hover:bg-purple-700 text-white text-base"
              >
                Complete Setup
                <CheckIcon className="ml-2 h-5 w-5" />
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
