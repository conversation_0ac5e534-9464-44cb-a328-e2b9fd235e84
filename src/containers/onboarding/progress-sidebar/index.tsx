import { cn } from '@/lib/utils';
import { OnboardingStep } from '@/types';
import { CheckIcon } from 'lucide-react';

type SidebarProgressProps = {
  currentStep: OnboardingStep;
  steps: OnboardingStep[];
  completedSteps: string[];
  onSelectStep: (step: OnboardingStep) => void;
};

const stepLabels: Record<OnboardingStep, string> = {
  domain: 'Base setup',
  plugin: 'Plugin Setup',
  credentials: 'WordPress setup',
  period: 'Media Plan',
  budget: 'Budget setup',
};

export default function ProgressSidebar({
  currentStep,
  steps,
  completedSteps,
  onSelectStep,
}: SidebarProgressProps) {
  const isCompleted = (step: OnboardingStep) => {
    return (
      completedSteps.includes(step) ||
      (step === 'plugin' && completedSteps.includes('pluginVerified'))
    );
  };

  const getStepIndex = (step: OnboardingStep) => {
    return steps.indexOf(step) + 1;
  };

  return (
    <div className="bg-primary rounded-lg shadow-sm p-6">
      <h2 className="text-white font-medium text-lg mb-6">
        {currentStep === 'domain' ? 'Quick & Easy Setup' : 'Getting Started'}
      </h2>

      <div className="space-y-6">
        {steps.map(step => {
          const isStepCompleted = isCompleted(step);
          const isCurrentStep = step === currentStep;

          return (
            <div
              key={step}
              className="flex items-center cursor-pointer"
              onClick={() => onSelectStep(step)}
            >
              <div
                className={cn(
                  'flex items-center justify-center w-8 h-8 rounded-full border-2 mr-3 transition-colors',
                  isStepCompleted
                    ? 'bg-white text-primary border-white'
                    : isCurrentStep
                      ? 'bg-transparent text-white border-white'
                      : 'bg-transparent text-white/60 border-white/30'
                )}
              >
                {isStepCompleted ? (
                  <CheckIcon className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-medium">
                    {getStepIndex(step)}
                  </span>
                )}
              </div>

              <div>
                <h3
                  className={cn(
                    'text-sm font-medium',
                    isCurrentStep
                      ? 'text-white'
                      : isStepCompleted
                        ? 'text-white'
                        : 'text-white/60'
                  )}
                >
                  {stepLabels[step]}
                </h3>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-8 pt-6 border-t border-white/20">
        <div className="text-white/80 text-sm">
          <p className="font-medium mb-2">What you'll get:</p>
          <ul className="space-y-1 text-xs">
            <li>✓ WordPress integration</li>
            <li>✓ SEO-optimized content</li>
            <li>✓ Automated publishing</li>
            <li>✓ Performance analytics</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
