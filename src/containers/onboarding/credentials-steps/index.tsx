import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, AlertCircle, ArrowRightIcon } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

type Step3CredentialsProps = {
  onNext: () => void;
  initialValues?: {
    username?: string;
    password?: string;
  };
  onUpdate: (credentials: { username: string; password: string }) => void;
};

const wordpressCredentialsSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

type FormValues = z.infer<typeof wordpressCredentialsSchema>;

export default function CredentialsStep({
  onNext,
  initialValues,
  onUpdate,
}: Step3CredentialsProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(wordpressCredentialsSchema),
    defaultValues: {
      username: initialValues?.username || '',
      password: initialValues?.password || '',
    },
  });

  const onSubmit = (values: FormValues) => {
    onUpdate(values);
    onNext();
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="bg-white shadow-lg border-0">
        <CardHeader className="text-center pb-4">
          <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <Shield className="w-8 h-8 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            WordPress Credentials
          </CardTitle>
          <p className="text-gray-600 mt-2">
            Enter your WordPress admin credentials to connect your site.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Your credentials are encrypted and stored securely. We only use
              them to manage your content.
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-medium text-gray-700">
                        WordPress Username
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="admin"
                          className="mt-1 h-12"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-medium text-gray-700">
                        WordPress Password
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="password"
                          placeholder="Enter your WordPress password"
                          className="mt-1 h-12"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">
                  Security Note
                </h4>
                <p className="text-sm text-blue-700">
                  Make sure you have administrator privileges on your WordPress
                  site. We need these permissions to publish and manage content
                  on your behalf.
                </p>
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white text-base"
                disabled={!form.formState.isValid}
              >
                Continue to Media Plan
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
