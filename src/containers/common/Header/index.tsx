import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { BellIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

type HeaderProps = {
  showNav?: boolean;
  showCredits?: boolean;
  credits?: number;
};

export default function Header({
  showNav = false,
  showCredits = false,
  credits = 0,
}: HeaderProps) {
  const [user] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://github.com/shadcn.png',
  });

  return (
    <header className="bg-white border-b border-gray-200 py-4">
      <div className="container mx-auto px-4 flex items-center justify-between">
        <div className="flex items-center space-x-8">
          <Link href="/" className="text-primary font-semibold text-xl">
            SEO<span className="text-secondary">45</span>
          </Link>

          {showNav && (
            <nav className="hidden md:flex space-x-6">
              <Link href="/dashboard" className="text-gray-900 font-medium">
                Home
              </Link>
              <Link
                href="/dashboard/writer"
                className="text-gray-500 hover:text-gray-900"
              >
                AI Writer
              </Link>
              <Link
                href="/dashboard/analytics"
                className="text-gray-500 hover:text-gray-900"
              >
                Analytics
              </Link>
              <Link
                href="/dashboard/settings"
                className="text-gray-500 hover:text-gray-900"
              >
                Settings
              </Link>
            </nav>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {showCredits && (
            <div className="text-sm text-gray-600">
              Credits:{' '}
              <span className="font-medium text-primary">{credits}</span>
            </div>
          )}

          <Button variant="ghost" size="sm" className="relative">
            <BellIcon className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              3
            </span>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback>
                    {user.name
                      .split(' ')
                      .map(n => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user.name}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile">Profile</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings">Settings</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/billing">Billing</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Log out</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
