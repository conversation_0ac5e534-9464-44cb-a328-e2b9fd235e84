import { z } from 'zod';

export const planSchema = z.object({
  name: z
    .string()
    .min(1, 'Plan name is required')
    .max(100, 'Plan name is too long'),
  description: z
    .string()
    .min(1, 'Description is required')
    .max(500, 'Description is too long'),
  price: z.number().min(0, 'Price must be a positive number'),
  discounted_price: z
    .number()
    .min(0, 'Discounted price must be positive')
    .nullable()
    .optional(),
  interval: z.enum(['month', 'year'], {
    required_error: 'Billing interval is required',
  }),
  features: z.record(z.boolean()).optional().default({}),
  max_websites: z.number().min(1, 'Must allow at least 1 website').optional(),
  max_keywords: z.number().min(1, 'Must allow at least 1 keyword').optional(),
  is_popular: z.boolean().default(false),
  is_active: z.boolean().default(true),
  sort_order: z.number().default(0),
});

export const insertPlanSchema = planSchema;
export const updatePlanSchema = planSchema.partial();

export type PlanFormData = z.infer<typeof planSchema>;
export type InsertPlanData = z.infer<typeof insertPlanSchema>;
export type UpdatePlanData = z.infer<typeof updatePlanSchema>;

// Common feature options
export const COMMON_FEATURES = [
  'SEO Analysis',
  'Keyword Tracking',
  'Competitor Analysis',
  'Site Audit',
  'Backlink Monitoring',
  'SERP Tracking',
  'Content Optimization',
  'Technical SEO',
  'Local SEO',
  'Mobile Optimization',
  'Page Speed Analysis',
  'Schema Markup',
  'Social Media Integration',
  'Analytics Integration',
  'White Label Reports',
  '24/7 Support',
  'Priority Support',
  'Custom Integrations',
  'API Access',
  'Data Export',
] as const;
