import { z } from 'zod';

// Subscription status enum
export const SUBSCRIPTION_STATUSES = [
  { value: 'active', label: 'Active', variant: 'default' as const },
  { value: 'trialing', label: 'Trialing', variant: 'secondary' as const },
  { value: 'past_due', label: 'Past Due', variant: 'destructive' as const },
  { value: 'canceled', label: 'Canceled', variant: 'outline' as const },
  { value: 'incomplete', label: 'Incomplete', variant: 'secondary' as const },
  {
    value: 'incomplete_expired',
    label: 'Incomplete Expired',
    variant: 'destructive' as const,
  },
  { value: 'unpaid', label: 'Unpaid', variant: 'destructive' as const },
  { value: 'paused', label: 'Paused', variant: 'secondary' as const },
] as const;

export const SUBSCRIPTION_STATUS_VALUES = SUBSCRIPTION_STATUSES.map(
  s => s.value
);

const baseSubscriptionSchema = z.object({
  status: z.enum(SUBSCRIPTION_STATUS_VALUES as [string, ...string[]], {
    required_error: 'Status is required',
  }),
  cancel_at_period_end: z.boolean().default(false),
  trial_start: z
    .string()
    .optional()
    .nullable()
    .refine(date => !date || !isNaN(Date.parse(date)), {
      message: 'Invalid trial start date',
    }),
  trial_end: z
    .string()
    .optional()
    .nullable()
    .refine(date => !date || !isNaN(Date.parse(date)), {
      message: 'Invalid trial end date',
    }),
  current_period_start: z.string().refine(date => !isNaN(Date.parse(date)), {
    message: 'Invalid current period start date',
  }),
  current_period_end: z.string().refine(date => !isNaN(Date.parse(date)), {
    message: 'Invalid current period end date',
  }),
});

export const subscriptionUpdateSchema = baseSubscriptionSchema
  .refine(
    data => {
      if (data.trial_start && data.trial_end) {
        return new Date(data.trial_start) <= new Date(data.trial_end);
      }
      return true;
    },
    {
      message: 'Trial end date must be after trial start date',
      path: ['trial_end'],
    }
  )
  .refine(
    data => {
      return (
        new Date(data.current_period_start) <= new Date(data.current_period_end)
      );
    },
    {
      message: 'Current period end date must be after start date',
      path: ['current_period_end'],
    }
  );

export type SubscriptionUpdateData = z.infer<typeof subscriptionUpdateSchema>;

// Helper functions
export const getStatusBadgeVariant = (status: string) => {
  const statusObj = SUBSCRIPTION_STATUSES.find(s => s.value === status);
  return statusObj?.variant || 'default';
};

export const getStatusLabel = (status: string) => {
  const statusObj = SUBSCRIPTION_STATUSES.find(s => s.value === status);
  return statusObj?.label || status;
};

export const isActiveSubscription = (status: string) => {
  return ['active', 'trialing'].includes(status);
};

export const isExpiredSubscription = (status: string) => {
  return ['canceled', 'incomplete_expired', 'unpaid'].includes(status);
};

export const canCancelSubscription = (status: string) => {
  return ['active', 'trialing', 'past_due'].includes(status);
};

export const canReactivateSubscription = (status: string) => {
  return ['canceled', 'paused'].includes(status);
};
