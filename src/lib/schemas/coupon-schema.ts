import { z } from 'zod';

const baseCouponSchema = z.object({
  code: z
    .string()
    .min(3, 'Coupon code must be at least 3 characters')
    .max(50, 'Coupon code must be less than 50 characters')
    .regex(
      /^[A-Z0-9_-]+$/,
      'Coupon code can only contain uppercase letters, numbers, hyphens, and underscores'
    ),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional()
    .nullable(),
  discount_type: z.enum(['percentage', 'fixed'], {
    required_error: 'Discount type is required',
  }),
  discount_value: z
    .number()
    .min(0, 'Discount value must be positive')
    .max(100000, 'Discount value is too large'),
  is_active: z.boolean().default(true),
  max_uses: z
    .number()
    .min(1, 'Maximum uses must be at least 1')
    .max(1000000, 'Maximum uses is too large')
    .optional()
    .nullable(),
  current_uses: z
    .number()
    .min(0, 'Current uses cannot be negative')
    .default(0)
    .optional(),
  valid_from: z
    .string()
    .optional()
    .nullable()
    .refine(date => !date || !isNaN(Date.parse(date)), {
      message: 'Invalid start date',
    }),
  valid_until: z
    .string()
    .optional()
    .nullable()
    .refine(date => !date || !isNaN(Date.parse(date)), {
      message: 'Invalid end date',
    }),
  minimum_order_amount: z
    .number()
    .min(0, 'Minimum order amount must be positive')
    .optional()
    .nullable(),
  usage_limit_per_user: z
    .number()
    .min(1, 'Usage limit per user must be at least 1')
    .optional()
    .nullable(),
  applicable_plans: z.array(z.number()).optional().default([]),
});

export const couponSchema = baseCouponSchema
  .refine(
    data => {
      if (data.discount_type === 'percentage' && data.discount_value > 100) {
        return false;
      }
      return true;
    },
    {
      message: 'Percentage discount cannot exceed 100%',
      path: ['discount_value'],
    }
  )
  .refine(
    data => {
      if (data.valid_from && data.valid_until) {
        return new Date(data.valid_from) <= new Date(data.valid_until);
      }
      return true;
    },
    {
      message: 'End date must be after start date',
      path: ['valid_until'],
    }
  );

export const insertCouponSchema = couponSchema;
export const updateCouponSchema = baseCouponSchema.partial();

export type CouponFormData = z.infer<typeof couponSchema>;
export type InsertCouponData = z.infer<typeof insertCouponSchema>;
export type UpdateCouponData = z.infer<typeof updateCouponSchema>;

// Coupon discount types
export const DISCOUNT_TYPES = [
  { value: 'percentage', label: 'Percentage (%)', icon: '%' },
  { value: 'fixed', label: 'Fixed Amount ($)', icon: '$' },
] as const;

// Common coupon templates
export const COUPON_TEMPLATES = [
  {
    name: 'Welcome Discount',
    code: 'WELCOME10',
    description: 'Welcome new customers with a 10% discount',
    discount_type: 'percentage' as const,
    discount_value: 10,
    max_uses: 100,
  },
  {
    name: 'First Month Free',
    code: 'FIRSTFREE',
    description: 'First month completely free for new subscribers',
    discount_type: 'percentage' as const,
    discount_value: 100,
    max_uses: 50,
  },
  {
    name: 'Holiday Special',
    code: 'HOLIDAY25',
    description: '25% off for holiday season',
    discount_type: 'percentage' as const,
    discount_value: 25,
    max_uses: 200,
  },
  {
    name: 'Flash Sale',
    code: 'FLASH50',
    description: '$50 off for limited time',
    discount_type: 'fixed' as const,
    discount_value: 50,
    max_uses: 25,
  },
] as const;
