import { z } from 'zod';

export const domainFormSchema = z.object({
  domain: z
    .string()
    .min(1, 'Domain is required')
    .regex(
      /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
      'Please enter a valid domain (e.g., example.com)'
    )
    .transform(val => val.toLowerCase().trim()),
  wordpressUrl: z
    .string()
    .optional()
    .refine(
      val => !val || z.string().url().safeParse(val).success,
      'Please enter a valid URL'
    ),
});

export const mediaPlanSchema = z.object({
  articlesPerWeek: z
    .number()
    .min(1, 'Must publish at least 1 article per week')
    .max(50, 'Cannot exceed 50 articles per week'),
  contentStyle: z.enum(['informational', 'promotional', 'mixed']),
  targetAudience: z.string().min(1, 'Target audience is required'),
  keywords: z.array(z.string()).min(1, 'At least one keyword is required'),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  frequency: z.enum(['daily', 'weekly', 'monthly']).optional(),
});

export const budgetSchema = z.object({
  monthlyBudget: z
    .number()
    .min(10, 'Minimum budget is $10')
    .max(10000, 'Maximum budget is $10,000'),
  subscriptionPlan: z.enum(['basic', 'pro', 'enterprise']),
  paymentMethod: z.enum(['credit_card', 'paypal', 'bank_transfer']),
  amount: z
    .number()
    .min(10, 'Minimum amount is $10')
    .max(10000, 'Maximum amount is $10,000')
    .optional(),
  currency: z.string().optional(),
});

// Schema for period step (extracted from mediaPlanSchema)
export const periodSchema = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  frequency: z.enum(['daily', 'weekly', 'monthly']).optional(),
});

export type DomainFormData = z.infer<typeof domainFormSchema>;
export type MediaPlanData = z.infer<typeof mediaPlanSchema>;
export type BudgetData = z.infer<typeof budgetSchema>;
export type PeriodData = z.infer<typeof periodSchema>;
