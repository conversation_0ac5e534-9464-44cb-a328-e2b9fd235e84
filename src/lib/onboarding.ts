// Local storage keys
const STORAGE_KEY = 'seo45_onboarding_data';

// Type for onboarding data
export interface OnboardingData {
  [key: string]: any;
  domain?: string;
  pluginVerified?: boolean;
  credentials?: {
    username: string;
    password: string;
  };
  mediaPlan?: {
    startDate: Date;
    endDate: Date;
    frequency: 'daily' | 'weekly' | 'monthly';
  };
  budget?: {
    amount: number;
    currency: string;
  };
}

// Get onboarding data from localStorage
export function getOnboardingData(): OnboardingData {
  try {
    const data = localStorage.getItem(STORAGE_KEY);
    if (!data) return {};

    const parsedData = JSON.parse(data);

    // Convert string dates back to Date objects
    if (parsedData.mediaPlan) {
      parsedData.mediaPlan.startDate = new Date(parsedData.mediaPlan.startDate);
      parsedData.mediaPlan.endDate = new Date(parsedData.mediaPlan.endDate);
    }

    return parsedData;
  } catch (error) {
    console.error('Error getting onboarding data:', error);
    return {};
  }
}

// Save onboarding data to localStorage
export function setOnboardingData(data: OnboardingData): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving onboarding data:', error);
  }
}

// Clear onboarding data from localStorage
export function clearOnboardingData(): void {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing onboarding data:', error);
  }
}

// Check if onboarding is complete
export function isOnboardingComplete(): boolean {
  const data = getOnboardingData();
  return !!(
    data.domain &&
    data.pluginVerified &&
    data.credentials?.username &&
    data.credentials?.password &&
    data.mediaPlan &&
    data.budget
  );
}
