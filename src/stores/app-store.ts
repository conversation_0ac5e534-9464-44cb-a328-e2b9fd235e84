import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  plan: 'free' | 'pro' | 'enterprise';
}

interface AppState {
  // User state
  user: User | null;
  isAuthenticated: boolean;

  // UI state
  theme: 'light' | 'dark' | 'system';
  sidebarCollapsed: boolean;

  // Credits and notifications
  credits: number;
  notifications: Array<{
    id: string;
    title: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    timestamp: Date;
    read: boolean;
  }>;

  // Actions
  setUser: (user: User | null) => void;
  setAuthenticated: (isAuthenticated: boolean) => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  toggleSidebar: () => void;
  setCredits: (credits: number) => void;
  addNotification: (
    notification: Omit<AppState['notifications'][0], 'id' | 'timestamp'>
  ) => void;
  markNotificationAsRead: (id: string) => void;
  clearNotifications: () => void;
  logout: () => void;
}

export const useAppStore = create<AppState>()(
  persist(
    set => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      theme: 'system',
      sidebarCollapsed: false,
      credits: 1250,
      notifications: [
        {
          id: '1',
          title: 'Welcome!',
          message: 'Your account has been created successfully.',
          type: 'success',
          timestamp: new Date(),
          read: false,
        },
        {
          id: '2',
          title: 'Credits Added',
          message: 'You have received 1000 bonus credits.',
          type: 'info',
          timestamp: new Date(),
          read: false,
        },
        {
          id: '3',
          title: 'New Feature',
          message: 'Check out our new AI Writer improvements.',
          type: 'info',
          timestamp: new Date(),
          read: false,
        },
      ],

      // Actions
      setUser: user => set({ user, isAuthenticated: !!user }),
      setAuthenticated: isAuthenticated => set({ isAuthenticated }),
      setTheme: theme => set({ theme }),
      toggleSidebar: () =>
        set(state => ({ sidebarCollapsed: !state.sidebarCollapsed })),
      setCredits: credits => set({ credits }),

      addNotification: notification =>
        set(state => ({
          notifications: [
            {
              ...notification,
              id: Date.now().toString(),
              timestamp: new Date(),
            },
            ...state.notifications,
          ],
        })),

      markNotificationAsRead: id =>
        set(state => ({
          notifications: state.notifications.map(notification =>
            notification.id === id
              ? { ...notification, read: true }
              : notification
          ),
        })),

      clearNotifications: () => set({ notifications: [] }),

      logout: () =>
        set({
          user: null,
          isAuthenticated: false,
          credits: 0,
          notifications: [],
        }),
    }),
    {
      name: 'app-storage',
      partialize: state => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
        credits: state.credits,
      }),
    }
  )
);
