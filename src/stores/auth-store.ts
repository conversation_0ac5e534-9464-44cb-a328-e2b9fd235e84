import { supabase } from '@/supabase/supabase';
import { Profile } from '@/supabase/types';
import { AuthError, Session, User } from '@supabase/supabase-js';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthResult {
  error: AuthError | null;
}

interface AuthState {
  // State
  user: User | null;
  profile: Profile | null;
  session: Session | null;
  loading: boolean;
  isAuthenticated: boolean;

  // Actions
  signIn: (email: string, password: string) => Promise<AuthResult>;
  signUp: (
    email: string,
    password: string,
    fullName?: string
  ) => Promise<AuthResult>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<AuthResult>;
  setUser: (user: User | null) => void;
  setProfile: (profile: Profile | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  initialize: () => Promise<void>;
  fetchProfile: (userId: string) => Promise<void>;

  // Helper getters
  isAdmin: () => boolean;
  getRedirectPath: () => string;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      profile: null,
      session: null,
      loading: true,
      isAuthenticated: false,

      // Actions
      setUser: (user: User | null) => set({ user, isAuthenticated: !!user }),

      setProfile: (profile: Profile | null) => set({ profile }),

      setSession: (session: Session | null) =>
        set({
          session,
          user: session?.user ?? null,
          isAuthenticated: !!session?.user,
        }),

      setLoading: (loading: boolean) => set({ loading }),

      fetchProfile: async (userId: string) => {
        try {
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

          if (error) {
            console.error('Error fetching profile:', error);
            set({ profile: null });
            return;
          }

          set({ profile });
        } catch (error) {
          console.error('Error fetching profile:', error);
          set({ profile: null });
        }
      },

      initialize: async () => {
        set({ loading: true });
        try {
          // Get initial session
          const {
            data: { session },
            error,
          } = await supabase.auth.getSession();

          if (error) {
            console.error('Session error:', error);
          }

          set({
            session,
            user: session?.user ?? null,
            isAuthenticated: !!session?.user,
          });

          // Fetch profile if user exists
          if (session?.user) {
            await get().fetchProfile(session.user.id);
          }

          set({ loading: false });

          // Listen for auth changes
          supabase.auth.onAuthStateChange(async (event, session) => {
            set({
              session,
              user: session?.user ?? null,
              isAuthenticated: !!session?.user,
            });

            // Fetch profile when user signs in
            if (session?.user && event === 'SIGNED_IN') {
              await get().fetchProfile(session.user.id);
            }

            // Clear profile when user signs out
            if (event === 'SIGNED_OUT') {
              set({ profile: null });
            }
          });
        } catch (error) {
          console.error('Auth initialization error:', error);
          set({ loading: false });
        }
      },

      signIn: async (email: string, password: string): Promise<AuthResult> => {
        try {
          const { error } = await supabase.auth.signInWithPassword({
            email,
            password,
          });
          return { error };
        } catch (error) {
          return { error: error as AuthError };
        }
      },

      signUp: async (
        email: string,
        password: string,
        fullName?: string
      ): Promise<AuthResult> => {
        try {
          const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                full_name: fullName,
              },
            },
          });

          // Create profile if sign up was successful and user was created
          if (!error && data.user) {
            try {
              await supabase.from('profiles').insert({
                id: data.user.id,
                email: email,
                full_name: fullName || null,
                role: 'user', // Default role
              });
            } catch (profileError) {
              console.error('Error creating profile:', profileError);
              // Don't return an error here as the auth user was created successfully
            }
          }

          return { error };
        } catch (error) {
          return { error: error as AuthError };
        }
      },

      signOut: async (): Promise<void> => {
        try {
          const { error } = await supabase.auth.signOut();
          if (error) {
            console.error('Sign out error:', error);
          }
          // Clear local state
          set({
            user: null,
            profile: null,
            session: null,
            isAuthenticated: false,
          });
        } catch (error) {
          console.error('Sign out error:', error);
        }
      },

      resetPassword: async (email: string): Promise<AuthResult> => {
        try {
          const { error } = await supabase.auth.resetPasswordForEmail(email);
          return { error };
        } catch (error) {
          return { error: error as AuthError };
        }
      },

      // Helper methods
      isAdmin: () => {
        const state = get();
        return state.profile?.role === 'admin';
      },

      getRedirectPath: () => {
        const state = get();
        if (!state.isAuthenticated) {
          return '/auth';
        }

        // If user is admin, redirect to admin dashboard
        if (state.profile?.role === 'admin') {
          return '/admin';
        }

        // Default to dashboard for regular users
        return '/dashboard';
      },
    }),
    {
      name: 'auth-storage',
      partialize: state => ({
        user: state.user,
        profile: state.profile,
        session: state.session,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Convenience hook that matches the old useAuth API
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    profile: store.profile,
    session: store.session,
    loading: store.loading,
    isAuthenticated: store.isAuthenticated,
    signIn: store.signIn,
    signUp: store.signUp,
    signOut: store.signOut,
    resetPassword: store.resetPassword,
    isAdmin: store.isAdmin,
    getRedirectPath: store.getRedirectPath,
  };
};
