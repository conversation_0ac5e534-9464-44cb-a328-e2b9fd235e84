import { create } from 'zustand';

interface User {
  name: string;
  email: string;
  avatar: string;
}

interface HeaderState {
  user: User;
  notifications: number;
  credits: number;
  isSearchOpen: boolean;
  isMobileMenuOpen: boolean;

  // Actions
  setUser: (user: User) => void;
  setNotifications: (count: number) => void;
  setCredits: (credits: number) => void;
  toggleSearch: () => void;
  toggleMobileMenu: () => void;
  clearNotifications: () => void;
}

export const useHeaderStore = create<HeaderState>(set => ({
  user: {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
  },
  notifications: 3,
  credits: 1250,
  isSearchOpen: false,
  isMobileMenuOpen: false,

  // Actions
  setUser: user => set({ user }),
  setNotifications: notifications => set({ notifications }),
  setCredits: credits => set({ credits }),
  toggleSearch: () => set(state => ({ isSearchOpen: !state.isSearchOpen })),
  toggleMobileMenu: () =>
    set(state => ({ isMobileMenuOpen: !state.isMobileMenuOpen })),
  clearNotifications: () => set({ notifications: 0 }),
}));
