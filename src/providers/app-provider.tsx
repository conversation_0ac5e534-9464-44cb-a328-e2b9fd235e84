import { useAppStore } from '@/stores';
import { ReactNode, useEffect } from 'react';

interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const { setUser, setAuthenticated } = useAppStore();

  useEffect(() => {
    // Initialize app state on mount
    // This is where you could check for existing auth tokens, etc.

    // For demo purposes, set a default user
    const demoUser = {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: '/api/placeholder/40/40',
      plan: 'pro' as const,
    };

    setUser(demoUser);
    setAuthenticated(true);
  }, [setUser, setAuthenticated]);

  return <>{children}</>;
}
