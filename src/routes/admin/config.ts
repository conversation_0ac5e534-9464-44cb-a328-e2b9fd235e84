import { lazy } from 'react';
import { RouteConfig } from '../types';

const AdminDashboardPage = lazy(() => import('@/pages/admin/dashboard'));
const AdminUsersPage = lazy(() => import('@/pages/admin/users/users-page'));
const AdminSubscriptionsPage = lazy(
  () => import('@/pages/admin/subscriptions/subscriptions-page')
);
const AdminCouponsPage = lazy(
  () => import('@/pages/admin/coupons/coupons-page')
);
const AdminPlansPage = lazy(() => import('@/pages/admin/plans/plans-page'));
const AdminWebsitesPage = lazy(
  () => import('@/pages/admin/websites/websites-page')
);
const AdminTransactionsPage = lazy(
  () => import('@/pages/admin/transactions/transactions-page')
);

export const adminRoutes: RouteConfig[] = [
  {
    path: '/admin',
    component: AdminDashboardPage,
    protected: true,
    layout: 'admin',
  },
  {
    path: '/admin/users',
    component: AdminUsersPage,
    protected: true,
    layout: 'admin',
  },
  {
    path: '/admin/subscriptions',
    component: AdminSubscriptionsPage,
    protected: true,
    layout: 'admin',
  },
  {
    path: '/admin/coupons',
    component: AdminCouponsPage,
    protected: true,
    layout: 'admin',
  },
  {
    path: '/admin/plans',
    component: AdminPlansPage,
    protected: true,
    layout: 'admin',
  },
  {
    path: '/admin/websites',
    component: AdminWebsitesPage,
    protected: true,
    layout: 'admin',
  },
  {
    path: '/admin/transactions',
    component: AdminTransactionsPage,
    protected: true,
    layout: 'admin',
  },
];
