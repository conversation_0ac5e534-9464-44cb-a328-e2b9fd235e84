import AdminLayout from '@/components/layout/admin/admin-layout';
import React from 'react';
import { Route, RouteProps } from 'wouter';

type RouteParams = {
  [param: string]: string | undefined;
};

interface AdminLayoutRouteProps extends Omit<RouteProps, 'component'> {
  component: React.ComponentType<{ params?: RouteParams }>;
}

const AdminLayoutRoute: React.FC<AdminLayoutRouteProps> = ({
  component: Component,
  ...rest
}) => {
  return (
    <Route {...rest}>
      {params => (
        <AdminLayout>
          <Component params={params} />
        </AdminLayout>
      )}
    </Route>
  );
};

export default AdminLayoutRoute;
