import { Suspense } from 'react';
import { LazyWrapperProps } from '../types';

const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center  bg-background">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
      <p className="text-gray-600">Loading...</p>
    </div>
  </div>
);

export default function LazyWrapper({ children }: LazyWrapperProps) {
  return <Suspense fallback={<LoadingFallback />}>{children}</Suspense>;
}
