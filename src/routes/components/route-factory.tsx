import { RouteConfig } from '../types';
import AdminRouteWrapper from './admin-route-wrapper';
import DashboardRouteWrapper from './dashboard-route-wrapper';
import LazyWrapper from './lazy-wrapper';
import ProtectedRoute from './protected-route';
import RootLayoutWrapper from './root-layout-wrapper';

export function createRouteElement(route: RouteConfig) {
  const { component: Component, protected: isProtected, layout } = route;

  const baseComponent = (
    <LazyWrapper>
      <Component />
    </LazyWrapper>
  );

  let wrappedComponent = baseComponent;

  // Apply layout wrapper based on configuration
  if (layout === 'admin') {
    wrappedComponent = <AdminRouteWrapper>{baseComponent}</AdminRouteWrapper>;
  } else if (layout === 'dashboard') {
    wrappedComponent = (
      <DashboardRouteWrapper>{baseComponent}</DashboardRouteWrapper>
    );
  } else if (layout === 'root') {
    wrappedComponent = <RootLayoutWrapper>{baseComponent}</RootLayoutWrapper>;
  }
  // If layout is 'none' or undefined, no layout wrapper is applied

  // Apply protection if needed
  if (isProtected) {
    const isAdminRoute = layout === 'admin';
    return (
      <ProtectedRoute adminOnly={isAdminRoute}>
        {wrappedComponent}
      </ProtectedRoute>
    );
  }

  return wrappedComponent;
}
