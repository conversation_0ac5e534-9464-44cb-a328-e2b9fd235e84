import MainLoading from '@/components/loaders/main-loading';
import { useAuth } from '@/stores';
import { Navigate } from 'react-router-dom';

interface ProtectedRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export default function ProtectedRoute({
  children,
  adminOnly = false,
}: ProtectedRouteProps) {
  const { user, loading, isAuthenticated, isAdmin } = useAuth();

  if (loading) {
    return <MainLoading />;
  }

  if (!user || !isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // Check admin access for admin-only routes
  if (adminOnly && !isAdmin()) {
    // Non-admin trying to access admin route, redirect to dashboard
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
}
