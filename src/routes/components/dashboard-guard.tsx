import { useAuth } from '@/stores';
import { Navigate } from 'react-router-dom';

export default function DashboardGuard() {
  const { isAuthenticated, getRedirectPath } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // Always redirect from root to the appropriate dashboard
  const redirectPath = getRedirectPath();
  return <Navigate to={redirectPath} replace />;
}
