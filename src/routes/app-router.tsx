import { BrowserRouter, Route, Routes } from 'react-router-dom';
import AuthRouteGuard from './components/auth-route-guard';
import LazyWrapper from './components/lazy-wrapper';
import { createRouteElement } from './components/route-factory';
import { NotFoundPage, routes } from './routes';

export default function AppRouter() {
  return (
    <BrowserRouter>
      <AuthRouteGuard>
        <Routes>
          {routes.map((route, index) => {
            const routeElement = createRouteElement(route);
            // Use a combination of path and index for unique keys
            const key = route.index ? 'index' : route.path || `route-${index}`;
            return route.index ? (
              <Route key={key} index element={routeElement} />
            ) : (
              <Route key={key} path={route.path} element={routeElement} />
            );
          })}

          {/* Catch-all route for 404 */}
          <Route
            path="*"
            element={
              <LazyWrapper>
                <NotFoundPage />
              </LazyWrapper>
            }
          />
        </Routes>
      </AuthRouteGuard>
    </BrowserRouter>
  );
}
