import { AdminUserService } from '@/services/admin/user-management-service';
import {
  CreateUserData,
  UpdateUserData,
  UserFilters,
  UserSort,
  UserWithAuthData,
} from '@/types/admin/user-management';
import { useCallback, useEffect, useState } from 'react';

export const useUsers = () => {
  const [users, setUsers] = useState<UserWithAuthData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState<UserFilters>({});
  const [sort, setSort] = useState<UserSort>({
    field: 'created_at',
    direction: 'desc',
  });

  const fetchUsers = useCallback(
    async (page = 1, pageFilters = filters, pageSort = sort) => {
      try {
        setLoading(true);
        setError(null);
        const { users: fetchedUsers, total: fetchedTotal } =
          await AdminUserService.getAllUsersWithDetails(
            pageFilters,
            pageSort,
            page,
            50
          );
        setUsers(fetchedUsers);
        setTotal(fetchedTotal);
        setCurrentPage(page);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch users');
        console.error('Error fetching users:', err);
      } finally {
        setLoading(false);
      }
    },
    [filters, sort]
  );

  const createUser = async (userData: CreateUserData) => {
    try {
      const newUser = await AdminUserService.createCompleteUser(userData);
      await fetchUsers(currentPage, filters, sort); // Refresh the list
      return newUser;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to create user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateUser = async (userId: string, userData: UpdateUserData) => {
    try {
      const updatedProfile = await AdminUserService.updateUserProfile(
        userId,
        userData
      );
      // Update the user in the local state
      setUsers(prev =>
        prev.map(user =>
          user.id === userId ? { ...user, ...updatedProfile } : user
        )
      );
      return updatedProfile;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to update user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteUser = async (userId: string) => {
    try {
      await AdminUserService.deleteCompleteUser(userId);
      setUsers(prev => prev.filter(user => user.id !== userId));
      setTotal(prev => prev - 1);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to delete user';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const resetUserPassword = async (userId: string, newPassword: string) => {
    try {
      await AdminUserService.resetUserPassword(userId, newPassword);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to reset password';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const toggleUserStatus = async (userId: string, banned: boolean) => {
    try {
      await AdminUserService.toggleUserStatus(userId, banned);
      // Update the user status in local state (this is a simplified approach)
      setUsers(prev =>
        prev.map(user =>
          user.id === userId
            ? {
                ...user,
                auth_user: user.auth_user
                  ? {
                      ...user.auth_user,
                      ban_duration: banned ? 'indefinite' : 'none',
                    }
                  : undefined,
              }
            : user
        )
      );
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to update user status';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const getUserWithDetails = async (userId: string) => {
    try {
      return await AdminUserService.getUserWithDetails(userId);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch user details';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateFilters = (newFilters: UserFilters) => {
    setFilters(newFilters);
    fetchUsers(1, newFilters, sort);
  };

  const updateSort = (newSort: UserSort) => {
    setSort(newSort);
    fetchUsers(currentPage, filters, newSort);
  };

  const changePage = (page: number) => {
    fetchUsers(page, filters, sort);
  };

  const refreshUsers = () => {
    fetchUsers(currentPage, filters, sort);
  };

  const clearError = () => setError(null);

  // Initial fetch
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return {
    // Data
    users,
    total,
    currentPage,
    loading,
    error,
    filters,
    sort,

    // Actions
    createUser,
    updateUser,
    deleteUser,
    resetUserPassword,
    toggleUserStatus,
    getUserWithDetails,

    // Pagination and filtering
    updateFilters,
    updateSort,
    changePage,
    refreshUsers,
    clearError,
  };
};
