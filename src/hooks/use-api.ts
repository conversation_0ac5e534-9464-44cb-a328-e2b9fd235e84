import { submitOnboardingData } from '@/services/onboarding-submission';
import { OnboardingState } from '@/types/onboarding';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Hook for submitting onboarding data
export function useSubmitOnboarding() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, data }: { userId: string; data: OnboardingState }) =>
      submitOnboardingData(userId, data),
    onSuccess: () => {
      // Invalidate and refetch any related queries
      queryClient.invalidateQueries({ queryKey: ['websites'] });
      queryClient.invalidateQueries({ queryKey: ['media-plans'] });
      toast.success('Onboarding completed successfully!');
    },
    onError: (error: Error) => {
      toast.error('Failed to submit onboarding data', {
        description: error.message || 'Please try again later.',
      });
    },
  });
}

// Example query hook for fetching user websites
export function useUserWebsites(userId?: string) {
  return useQuery({
    queryKey: ['websites', userId],
    queryFn: async () => {
      if (!userId) return [];

      // This would be replaced with actual API call
      const response = await fetch(`/api/websites?userId=${userId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch websites');
      }
      return response.json();
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Example mutation hook for creating a website
export function useCreateWebsite() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (websiteData: Record<string, unknown>) => {
      const response = await fetch('/api/websites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(websiteData),
      });

      if (!response.ok) {
        throw new Error('Failed to create website');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['websites'] });
      toast.success('Website created successfully!');
    },
    onError: (error: Error) => {
      toast.error('Failed to create website', {
        description: error.message,
      });
    },
  });
}

// Hook for plugin verification
export function useVerifyPlugin() {
  return useMutation({
    mutationFn: async ({
      domain,
      pluginKey,
    }: {
      domain: string;
      pluginKey: string;
    }) => {
      // This would be replaced with actual API call to verify plugin
      const response = await fetch('/api/verify-plugin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain, pluginKey }),
      });

      if (!response.ok) {
        throw new Error('Plugin verification failed');
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success('Plugin verified successfully!');
    },
    onError: (error: Error) => {
      toast.error('Plugin verification failed', {
        description: error.message,
      });
    },
  });
}
