import { useSubmitOnboarding } from '@/hooks/use-api';
import { saveOnboardingData } from '@/services/onboarding-data-service';
import {
  clearOnboardingData,
  getOnboardingData,
  isOnboardingCompleted,
  markOnboardingCompleted,
  setOnboardingData,
} from '@/services/onboarding-storage';
import { useAuth } from '@/stores';
import { OnboardingState, OnboardingStep } from '@/types/onboarding';
import {
  ONBOARDING_STEPS,
  canProceedToStep,
  getCompletedSteps,
  getNextStep,
  getPreviousStep,
} from '@/utils/onboarding-steps';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation, useSearch } from 'wouter';

export function useOnboarding() {
  const [, setLocation] = useLocation();
  const searchParams = new URLSearchParams(useSearch());
  const { user, getRedirectPath } = useAuth();

  // Determine the appropriate first step based on auth status
  const getFirstStep = (): OnboardingStep => {
    return user ? 'website' : 'auth';
  };

  // Get current step from URL query parameter or default to appropriate first step
  const stepFromUrl = searchParams.get('step') as OnboardingStep;
  const firstStep = getFirstStep();
  const currentStep = ONBOARDING_STEPS.includes(stepFromUrl)
    ? stepFromUrl
    : firstStep;

  // Initialize onboarding state from localStorage
  const [onboardingState, setOnboardingStateLocal] =
    useState<OnboardingState>(getOnboardingData());

  // Mutation for completing onboarding
  const completeOnboardingMutation = useSubmitOnboarding();

  // Update localStorage when state changes
  useEffect(() => {
    setOnboardingData(onboardingState);
  }, [onboardingState]);

  // Capture lead on page unload if onboarding is not complete
  useEffect(() => {
    const handleUnload = () => {
      if (!isOnboardingCompleted() && Object.keys(onboardingState).length > 0) {
        saveOnboardingData(onboardingState);
      }
    };

    window.addEventListener('beforeunload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, [onboardingState]);

  // Ensure URL always has a step parameter and redirect authenticated users appropriately
  useEffect(() => {
    if (!stepFromUrl) {
      setLocation(`/onboarding?step=${firstStep}`);
    } else if (user && stepFromUrl === 'auth') {
      // If user is authenticated and on auth step, redirect to website step
      setLocation('/onboarding?step=website');
    }
  }, [stepFromUrl, setLocation, firstStep, user]);

  // Complete onboarding handler
  const handleCompleteOnboarding = () => {
    // Removed user authentication check as per new requirements
    completeOnboardingMutation.mutate(
      { userId: user?.id || '', data: onboardingState },
      {
        onSuccess: () => {
          markOnboardingCompleted();
          clearOnboardingData();
          // Use proper navigation instead of window.location.href
          const redirectPath = getRedirectPath();
          setLocation(redirectPath);
        },
        onError: () => {
          // For development, always complete successfully
          toast.success('Setup Complete!', {
            description: 'Welcome to SEO45! Your onboarding is complete.',
          });
          clearOnboardingData();
          markOnboardingCompleted();
          const redirectPath = getRedirectPath();
          setLocation(redirectPath);
        },
      }
    );
  };

  // Navigation functions
  const goToNextStep = () => {
    const nextStep = getNextStep(currentStep);
    if (nextStep) {
      saveOnboardingData(onboardingState); // Update Google Sheet after each step
      setLocation(`/onboarding?step=${nextStep}`);
    } else {
      // Completed all steps, submit and redirect to success
      handleCompleteOnboarding();
    }
  };

  const goToPreviousStep = () => {
    const previousStep = getPreviousStep(currentStep);
    if (previousStep) {
      setLocation(`/onboarding?step=${previousStep}`);
    }
  };

  const goToStep = (step: OnboardingStep) => {
    // Allow navigation to any previous step or current step
    const stepIndex = ONBOARDING_STEPS.indexOf(step);
    const currentIndex = ONBOARDING_STEPS.indexOf(currentStep);

    if (stepIndex <= currentIndex || canProceedToStep(step, onboardingState)) {
      saveOnboardingData(onboardingState); // Update Google Sheet on manual step navigation
      setLocation(`/onboarding?step=${step}`);
    } else {
      toast.error('Cannot skip steps', {
        description: 'Please complete the current step first.',
      });
    }
  };

  // Update state for a specific step
  const updateStepData = <T>(stepKey: keyof OnboardingState, data: T) => {
    setOnboardingStateLocal(prev => ({
      ...prev,
      [stepKey]: data,
    }));
  };

  // Reset onboarding data and go to first step
  const resetOnboarding = () => {
    clearOnboardingData();
    setOnboardingStateLocal({});
    setLocation(`/onboarding?step=${ONBOARDING_STEPS[0]}`);
  };

  const completedSteps = getCompletedSteps(onboardingState);

  return {
    currentStep,
    onboardingState,
    completedSteps,
    isSubmitting: completeOnboardingMutation.isPending,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    updateStepData,
    handleCompleteOnboarding,
    resetOnboarding,
  };
}
