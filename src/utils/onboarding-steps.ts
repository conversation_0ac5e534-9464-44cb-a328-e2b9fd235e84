import {
  OnboardingState,
  OnboardingStep,
  OnboardingStepConfig,
} from '@/types/onboarding';

export const ONBOARDING_STEPS: OnboardingStep[] = [
  'auth',
  'website',
  'plugin',
  'credentials',
  'plan',
  'review',
];

export const ONBOARDING_STEP_CONFIG: Record<
  OnboardingStep,
  OnboardingStepConfig
> = {
  auth: {
    id: 'auth',
    title: 'Create Account',
    description: 'Sign up to get started with SEO45',
    isCompleted: state => !!(state.auth?.email && state.auth?.password),
    validate: state => {
      const errors: string[] = [];
      if (!state.auth?.email?.trim()) errors.push('Email is required');
      if (!state.auth?.password?.trim()) errors.push('Password is required');
      if (state.auth?.email && !/\S+@\S+\.\S+/.test(state.auth.email)) {
        errors.push('Please enter a valid email address');
      }
      if (state.auth?.password && state.auth.password.length < 6) {
        errors.push('Password must be at least 6 characters long');
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  website: {
    id: 'website',
    title: 'Website',
    description: 'Enter your website details',
    isCompleted: state => !!state.website?.domain,
    validate: state => ({
      isValid: !!state.website?.domain,
      errors: !state.website?.domain ? ['Domain is required'] : [],
    }),
  },
  plugin: {
    id: 'plugin',
    title: 'Plugin',
    description: 'Install and verify the SEO45 WordPress plugin',
    isCompleted: state => state.plugin?.isVerified === true,
    validate: state => ({
      isValid: state.plugin?.isVerified === true,
      errors:
        state.plugin?.isVerified !== true ? ['Plugin must be verified'] : [],
    }),
  },
  credentials: {
    id: 'credentials',
    title: 'WordPress Credentials',
    description: 'Enter your WordPress admin credentials',
    isCompleted: state =>
      !!(state.credentials?.username && state.credentials?.password),
    validate: state => {
      const errors: string[] = [];
      if (!state.credentials?.username) errors.push('Username is required');
      if (!state.credentials?.password) errors.push('Password is required');
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  plan: {
    id: 'plan',
    title: 'Plan',
    description: 'Select a pricing plan',
    isCompleted: state => !!state.plan?.planId,
    validate: state => ({
      isValid: !!state.plan?.planId,
      errors: !state.plan?.planId ? ['Plan selection is required'] : [],
    }),
  },
  review: {
    id: 'review',
    title: 'Review',
    description: 'Review and confirm your details',
    isCompleted: () => true,
    validate: () => ({ isValid: true, errors: [] }),
  },
};

export function canProceedToStep(
  targetStep: OnboardingStep,
  state: OnboardingState
): boolean {
  const stepIndex = ONBOARDING_STEPS.indexOf(targetStep);

  // Can always access the first step
  if (stepIndex === 0) return true;

  // Check if all previous steps are completed
  return (
    stepIndex >= 0 &&
    ONBOARDING_STEPS.slice(0, stepIndex).every(step =>
      ONBOARDING_STEP_CONFIG[step].isCompleted(state)
    )
  );
}

export function getFilteredSteps(isAuthenticated: boolean): OnboardingStep[] {
  return isAuthenticated
    ? ONBOARDING_STEPS.filter(step => step !== 'auth')
    : ONBOARDING_STEPS;
}

export function getNextStep(
  currentStep: OnboardingStep
): OnboardingStep | null {
  const currentIndex = ONBOARDING_STEPS.indexOf(currentStep);
  if (currentIndex >= 0 && currentIndex < ONBOARDING_STEPS.length - 1) {
    return ONBOARDING_STEPS[currentIndex + 1];
  }
  return null;
}

export function getPreviousStep(
  currentStep: OnboardingStep
): OnboardingStep | null {
  const currentIndex = ONBOARDING_STEPS.indexOf(currentStep);
  if (currentIndex > 0) {
    return ONBOARDING_STEPS[currentIndex - 1];
  }
  return null;
}

export function getCompletedSteps(state: OnboardingState): OnboardingStep[] {
  return ONBOARDING_STEPS.filter(step =>
    ONBOARDING_STEP_CONFIG[step].isCompleted(state)
  );
}
