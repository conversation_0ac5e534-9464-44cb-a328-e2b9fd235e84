// Debug utility for onboarding
// You can run these in the browser console to test onboarding flow

// Clear all onboarding data
export function clearOnboardingData() {
  localStorage.removeItem('seo45_onboarding_data');
  localStorage.removeItem('seo45_onboarding_completed');
  console.log('Onboarding data cleared');
}

// Set sample data for testing
export function setSampleOnboardingData() {
  const sampleData = {
    domain: 'example.com',
    pluginVerified: false,
    credentials: undefined,
    mediaPlan: undefined,
    budget: undefined,
  };

  localStorage.setItem('seo45_onboarding_data', JSON.stringify(sampleData));
  console.log('Sample onboarding data set:', sampleData);
}

// Check current onboarding state
export function checkOnboardingState() {
  const data = localStorage.getItem('seo45_onboarding_data');
  const completed = localStorage.getItem('seo45_onboarding_completed');

  console.log('Current onboarding data:', data ? JSON.parse(data) : 'None');
  console.log('Onboarding completed:', completed);
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).onboardingDebug = {
    clear: clearOnboardingData,
    setSample: setSampleOnboardingData,
    check: checkOnboardingState,
  };

  console.log(
    'Onboarding debug utilities loaded. Use window.onboardingDebug in console'
  );
}
