import { OnboardingState, OnboardingStep } from '@/types/onboarding';
import { ONBOARDING_STEP_CONFIG } from './onboarding-steps';

export function validateStep(
  step: OnboardingStep,
  state: OnboardingState
): {
  isValid: boolean;
  errors: string[];
} {
  const config = ONBOARDING_STEP_CONFIG[step];
  return config.validate(state);
}

export function validateAllSteps(state: OnboardingState): {
  isValid: boolean;
  errors: Record<OnboardingStep, string[]>;
} {
  const errors: Record<OnboardingStep, string[]> = {} as Record<
    OnboardingStep,
    string[]
  >;
  let isValid = true;

  Object.entries(ONBOARDING_STEP_CONFIG).forEach(([step, config]) => {
    const stepValidation = config.validate(state);
    errors[step as OnboardingStep] = stepValidation.errors;
    if (!stepValidation.isValid) {
      isValid = false;
    }
  });

  return { isValid, errors };
}

export function isStepAccessible(
  targetStep: OnboardingStep,
  state: OnboardingState
): boolean {
  // First step is always accessible
  if (targetStep === 'auth') return true;

  // Check if previous steps are completed
  const stepOrder: OnboardingStep[] = [
    'auth',
    'website',
    'plugin',
    'credentials',
    'plan',
    'review',
  ];
  const targetIndex = stepOrder.indexOf(targetStep);

  for (let i = 0; i < targetIndex; i++) {
    const step = stepOrder[i];
    const config = ONBOARDING_STEP_CONFIG[step];
    if (!config.isCompleted(state)) {
      return false;
    }
  }

  return true;
}
