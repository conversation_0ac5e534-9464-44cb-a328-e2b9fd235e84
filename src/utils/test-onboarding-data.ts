/**
 * Test utility to verify onboarding data structure
 * Use this in browser console to test data saving
 */

import { saveOnboardingData } from '@/services/onboarding-data-service';
import { OnboardingState } from '@/types/onboarding';

export const testOnboardingData: OnboardingState = {
  auth: {
    email: '<EMAIL>',
    password: 'password123',
    fullName: 'Test User',
  },
  website: {
    domain: 'example.com',
    wordpressUrl: 'https://example.com/wp-admin',
  },
  plugin: {
    isVerified: true,
    pluginInstalled: true,
  },
  credentials: {
    username: 'wpuser',
    password: 'wppass',
  },
  plan: {
    planId: 'basic',
    planName: 'Basic Plan',
    price: 29.99,
    currency: 'USD',
  },
};

/**
 * Test function to manually trigger data saving
 * Call this from browser console: window.testSaveOnboardingData()
 */
export async function testSaveOnboardingData() {
  console.log('Testing onboarding data save with:', testOnboardingData);

  try {
    await saveOnboardingData(testOnboardingData);
    console.log('✅ Test completed successfully');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Make function available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).testSaveOnboardingData = testSaveOnboardingData;
  (window as any).testOnboardingData = testOnboardingData;

  console.log('🧪 Onboarding test utilities loaded. Use:');
  console.log('- window.testSaveOnboardingData() to test data saving');
  console.log('- window.testOnboardingData to see sample data structure');
}
