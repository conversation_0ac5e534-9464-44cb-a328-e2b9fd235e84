import { z } from 'zod';

// Form validation schemas
export const domainFormSchema = z.object({
  domain: z
    .string()
    .min(1, 'Domain is required')
    .refine(
      (val: string) =>
        /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/i.test(val),
      {
        message: 'Please enter a valid domain (e.g., example.com)',
      }
    ),
});

export const websiteFormSchema = z.object({
  domain: z.string().min(1, 'Domain is required'),
  name: z.string().min(1, 'Website name is required'),
});

export const wordpressCredentialsSchema = z.object({
  wordpress_url: z.string().url('Please enter a valid WordPress URL'),
  wordpress_username: z.string().min(1, 'Username is required'),
  wordpress_password: z
    .string()
    .min(6, 'Password must be at least 6 characters'),
});

export const mediaPlanSchema = z.object({
  start_date: z.date(),
  end_date: z.date(),
  frequency: z.enum(['daily', 'weekly', 'monthly']),
});

export const budgetSchema = z.object({
  budget_amount: z.number().min(1),
  budget_currency: z.string().default('USD'),
});

export const userFormSchema = z.object({
  full_name: z
    .string()
    .min(1, 'Full name is required')
    .max(100, 'Full name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username must be less than 30 characters')
    .optional()
    .nullable(),
  role: z
    .string()
    .min(1, 'Role is required')
    .max(50, 'Role must be less than 50 characters')
    .optional()
    .nullable(),
  website: z
    .string()
    .url('Please enter a valid website URL')
    .optional()
    .nullable()
    .or(z.literal('')),
  avatar_url: z
    .string()
    .url('Please enter a valid avatar URL')
    .optional()
    .nullable()
    .or(z.literal('')),
});

export type UserFormData = z.infer<typeof userFormSchema>;
