import { OnboardingData } from '@/types';

const ONBOARDING_STORAGE_KEY = 'onboarding_data';

export const getOnboardingData = (): OnboardingData => {
  try {
    const data = localStorage.getItem(ONBOARDING_STORAGE_KEY);
    return data ? JSON.parse(data) : {};
  } catch (error) {
    console.error('Error getting onboarding data:', error);
    return {};
  }
};

export const setOnboardingData = (data: OnboardingData): void => {
  try {
    localStorage.setItem(ONBOARDING_STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('Error setting onboarding data:', error);
  }
};

export const clearOnboardingData = (): void => {
  try {
    localStorage.removeItem(ONBOARDING_STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing onboarding data:', error);
  }
};
