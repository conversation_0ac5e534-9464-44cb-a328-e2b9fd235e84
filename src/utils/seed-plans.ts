import { createPlan } from '@/services/plan/plan-service';
import { InsertPlan } from '@/supabase/types';

export const seedPlans = async (): Promise<void> => {
  const plans: InsertPlan[] = [
    {
      name: 'Starter',
      description:
        'Perfect for individuals getting started with content creation',
      price: 29,
      discounted_price: null,
      interval: 'month',
      features: [
        'Up to 10 articles per month',
        'Basic SEO optimization',
        'WordPress integration',
        'Email support',
        'Content templates',
      ],
      is_active: true,
    },
    {
      name: 'Professional',
      description: 'Ideal for small businesses and content creators',
      price: 79,
      discounted_price: 59, // 25% discount
      interval: 'month',
      features: [
        'Up to 50 articles per month',
        'Advanced SEO optimization',
        'WordPress integration',
        'Priority support',
        'Analytics dashboard',
        'Custom content templates',
        'Keyword research tools',
        'Content calendar',
      ],
      is_active: true,
    },
    {
      name: 'Enterprise',
      description: 'Comprehensive solution for agencies and large businesses',
      price: 199,
      discounted_price: null,
      interval: 'month',
      features: [
        'Unlimited articles',
        'Premium SEO optimization',
        'WordPress integration',
        '24/7 phone support',
        'Advanced analytics',
        'Custom branding',
        'API access',
        'White-label solution',
        'Dedicated account manager',
        'Custom integrations',
      ],
      is_active: true,
    },
  ];

  try {
    for (const plan of plans) {
      await createPlan(plan);
      console.log(`Created plan: ${plan.name}`);
    }
    console.log('All plans seeded successfully');
  } catch (error) {
    console.error('Error seeding plans:', error);
    throw error;
  }
};

// Annual plans with discounts
export const seedAnnualPlans = async (): Promise<void> => {
  const annualPlans: InsertPlan[] = [
    {
      name: 'Starter Annual',
      description:
        'Perfect for individuals - Annual billing with 2 months free',
      price: 290,
      discounted_price: 240, // ~17% discount (10 months price)
      interval: 'year',
      features: [
        'Up to 10 articles per month',
        'Basic SEO optimization',
        'WordPress integration',
        'Email support',
        'Content templates',
        '2 months free',
      ],
      is_active: true,
    },
    {
      name: 'Professional Annual',
      description: 'Small businesses - Annual billing with 2 months free',
      price: 790,
      discounted_price: 590, // ~25% discount
      interval: 'year',
      features: [
        'Up to 50 articles per month',
        'Advanced SEO optimization',
        'WordPress integration',
        'Priority support',
        'Analytics dashboard',
        'Custom content templates',
        'Keyword research tools',
        'Content calendar',
        '2 months free',
      ],
      is_active: true,
    },
    {
      name: 'Enterprise Annual',
      description: 'Enterprise solution - Annual billing with 2 months free',
      price: 1990,
      discounted_price: 1590, // ~20% discount
      interval: 'year',
      features: [
        'Unlimited articles',
        'Premium SEO optimization',
        'WordPress integration',
        '24/7 phone support',
        'Advanced analytics',
        'Custom branding',
        'API access',
        'White-label solution',
        'Dedicated account manager',
        'Custom integrations',
        '2 months free',
      ],
      is_active: true,
    },
  ];

  try {
    for (const plan of annualPlans) {
      await createPlan(plan);
      console.log(`Created annual plan: ${plan.name}`);
    }
    console.log('All annual plans seeded successfully');
  } catch (error) {
    console.error('Error seeding annual plans:', error);
    throw error;
  }
};
