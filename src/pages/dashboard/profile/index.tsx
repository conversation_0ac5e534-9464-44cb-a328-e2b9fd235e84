'use client';

import {
  <PERSON>Header,
  ProfileInformation,
  ProfileSidebar,
} from '@/components/dashboard/profile';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { updateProfile } from '@/services/profile/profile-service';
import { useAuth } from '@/stores';
import { UpdateProfile } from '@/supabase/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { Camera, Mail, Shield } from 'lucide-react';
import { ChangeEvent, FormEvent, useEffect, useState } from 'react';

interface ProfileFormData {
  full_name: string;
  email: string;
  username: string;
  website: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ShowPasswordsState {
  current: boolean;
  new: boolean;
  confirm: boolean;
}

export default function ProfilePage() {
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Form states
  const [profileForm, setProfileForm] = useState<ProfileFormData>({
    full_name: profile?.full_name || '',
    email: profile?.email || user?.email || '',
    username: profile?.username || '',
    website: profile?.website || '',
  });

  const [passwordForm, setPasswordForm] = useState<PasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [showPasswords, setShowPasswords] = useState<ShowPasswordsState>({
    current: false,
    new: false,
    confirm: false,
  });

  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  // Update form when profile changes
  useEffect(() => {
    if (profile) {
      setProfileForm({
        full_name: profile.full_name || '',
        email: profile.email || user?.email || '',
        username: profile.username || '',
        website: profile.website || '',
      });
    }
  }, [profile, user?.email]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  // Profile update mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormData) => {
      if (!user?.id) throw new Error('User not authenticated');
      const updateData: UpdateProfile = {
        ...data,
        updated_at: new Date().toISOString(),
      };
      return updateProfile(user.id, updateData);
    },
    onSuccess: () => {
      toast({
        title: 'Profile Updated',
        description: 'Your profile has been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Update Failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Password change mutation
  const changePasswordMutation = useMutation({
    mutationFn: async (data: PasswordFormData) => {
      if (data.newPassword !== data.confirmPassword) {
        throw new Error('New passwords do not match');
      }

      // Import the service function dynamically to avoid IDE auto-formatting issues
      const { changePassword } = await import(
        '@/services/profile/profile-service'
      );
      return changePassword(data.newPassword);
    },
    onSuccess: () => {
      toast({
        title: 'Password Changed',
        description: 'Your password has been changed successfully.',
      });
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Password Change Failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Avatar upload mutation
  const uploadAvatarMutation = useMutation({
    mutationFn: async (file: File) => {
      if (!user?.id) throw new Error('User not authenticated');

      // Import the service function dynamically to avoid IDE auto-formatting issues
      const { uploadAvatar } = await import(
        '@/services/profile/profile-service'
      );
      return uploadAvatar(user.id, file);
    },
    onSuccess: () => {
      toast({
        title: 'Avatar Updated',
        description: 'Your profile picture has been updated successfully.',
      });
      setAvatarFile(null);
      setAvatarPreview(null);
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Upload Failed',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Form handlers
  const handleProfileFormChange = (
    field: keyof ProfileFormData,
    value: string
  ) => {
    setProfileForm(prev => ({ ...prev, [field]: value }));
  };

  const handlePasswordFormChange = (
    field: keyof PasswordFormData,
    value: string
  ) => {
    setPasswordForm(prev => ({ ...prev, [field]: value }));
  };

  const handleShowPasswordToggle = (field: keyof ShowPasswordsState) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const handleProfileSubmit = (e: FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate(profileForm);
  };

  const handlePasswordSubmit = (e: FormEvent) => {
    e.preventDefault();
    changePasswordMutation.mutate(passwordForm);
  };

  const handleAvatarFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAvatarUpload = () => {
    if (avatarFile) {
      uploadAvatarMutation.mutate(avatarFile);
    }
  };

  const handlePasswordReset = async () => {
    if (!user?.email) return;

    try {
      // Import the service function dynamically to avoid IDE auto-formatting issues
      const { resetPasswordForEmail } = await import(
        '@/services/profile/profile-service'
      );
      await resetPasswordForEmail(user.email);

      toast({
        title: 'Reset Email Sent',
        description: 'Check your email for password reset instructions.',
      });
    } catch (error: unknown) {
      toast({
        title: 'Reset Failed',
        description:
          error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    }
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="min-h-screen bg-card/70 px-4 rounded-xl"
    >
      <div className="container mx-auto px-4 py-8 space-y-8">
        <ProfileHeader />

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
          {/* Main Content Area - 60% */}
          <div className="lg:col-span-3 space-y-6">
            <ProfileInformation
              user={user}
              profile={profile}
              profileForm={profileForm}
              passwordForm={passwordForm}
              showPasswords={showPasswords}
              avatarFile={avatarFile}
              avatarPreview={avatarPreview}
              isUpdatingProfile={updateProfileMutation.isPending}
              isChangingPassword={changePasswordMutation.isPending}
              isUploadingAvatar={uploadAvatarMutation.isPending}
              onProfileFormChange={handleProfileFormChange}
              onPasswordFormChange={handlePasswordFormChange}
              onShowPasswordToggle={handleShowPasswordToggle}
              onProfileSubmit={handleProfileSubmit}
              onPasswordSubmit={handlePasswordSubmit}
              onAvatarFileChange={handleAvatarFileChange}
              onAvatarUpload={handleAvatarUpload}
            />

            {/* Quick Actions */}
            <Card className="border shadow-sm">
              <CardHeader>
                <CardTitle className="text-base font-semibold flex items-center gap-2">
                  <Shield className="h-4 w-4 text-primary" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={handlePasswordReset}
                >
                  <Mail className="h-4 w-4 mr-2" />
                  Send Reset Email
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={handleAvatarUpload}
                >
                  <Camera className="h-4 w-4 mr-2" />
                  Change Avatar
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Right Sidebar - 40% */}
          <div className="lg:col-span-2">
            <ProfileSidebar
              user={user}
              profile={profile}
              avatarPreview={avatarPreview}
              onAvatarUpload={() =>
                document.getElementById('avatar-upload')?.click()
              }
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
}
