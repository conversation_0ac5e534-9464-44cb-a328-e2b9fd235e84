import { getWebsitesByUser } from '@/services/website/website-service';
import { useAuth } from '@/stores';
import { Website } from '@/supabase/types';
import { useQuery } from '@tanstack/react-query';
import { WebsiteGrid } from './website-grid';

export default function WebsitesPage() {
  const { user } = useAuth();

  const { data: websites = [], isLoading: websitesLoading } = useQuery<
    Website[]
  >({
    queryKey: ['websites', user?.id],
    queryFn: () => (user ? getWebsitesByUser(user.id) : Promise.resolve([])),
    enabled: !!user,
  });

  const handleAddWebsite = () => {
    // Clear onboarding completion to allow re-entry
    localStorage.removeItem('onboarding_completed');
    // Force page reload to ensure proper routing
    window.location.href = '/onboarding';
  };

  return (
    <WebsiteGrid
      websites={websites}
      loading={websitesLoading}
      onAddWebsite={handleAddWebsite}
    />
  );
}
