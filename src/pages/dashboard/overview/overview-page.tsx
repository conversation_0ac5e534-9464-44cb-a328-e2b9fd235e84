import { getSubscriptions } from '@/services/subscription/subscription-service';
import { getWebsitesByUser } from '@/services/website/website-service';
import { useAuth } from '@/stores';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { OverviewTab } from './overview-tab';

export default function OverviewPage() {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch user's websites from Supabase
  const { data: websites = [], isLoading: websitesLoading } = useQuery({
    queryKey: ['websites', user?.id],
    queryFn: () => {
      if (!user?.id) throw new Error('User not found');
      return getWebsitesByUser(user.id);
    },
    enabled: !!user?.id,
  });

  // Fetch user's subscriptions from Supabase
  const { data: subscriptions = [], isLoading: subscriptionsLoading } =
    useQuery({
      queryKey: ['subscriptions', user?.id],
      queryFn: () => {
        if (!user?.id) throw new Error('User not found');
        return getSubscriptions(user.id);
      },
      enabled: !!user?.id,
    });

  const handleAddWebsite = () => {
    // Navigate to onboarding to add a new website
    navigate('/onboarding?step=website');
  };

  return (
    <OverviewTab
      websites={websites}
      subscriptions={subscriptions}
      loading={websitesLoading || subscriptionsLoading}
      onAddWebsite={handleAddWebsite}
    />
  );
}
