import {
  ArticlesContent,
  ArticlesHeader,
  ArticlesStats,
} from '@/components/dashboard/articles';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { getArticles } from '@/services/article/article-service';
import { useAuth } from '@/stores';
import { Article } from '@/types/article';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { useState } from 'react';

function ArticlesPage() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const {
    data: allArticles = [],
    isLoading: articlesLoading,
    error,
    refetch,
  } = useQuery<Article[]>({
    queryKey: ['articles', user?.id],
    queryFn: () => getArticles(user?.id || ''),
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Filter articles based on search and status
  const filteredArticles = allArticles.filter(article => {
    const matchesSearch = article.title
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || article.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  // Show error state if there's an error
  if (error && !articlesLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">Articles</h1>
          <p className="text-muted-foreground text-lg">
            Manage and monitor your AI-generated content
          </p>
        </div>

        <Card className="shadow-lg border-none bg-white dark:bg-zinc-900">
          <CardContent className="p-8 text-center">
            <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Unable to load articles</CardTitle>
              <CardDescription className="text-sm">
                {error.message ||
                  'There was a problem connecting to your Google Apps Script. Please check your setup.'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => refetch()} className="mt-4">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </CardContent>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Header Section */}
      <ArticlesHeader
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        onSearchChange={setSearchTerm}
        onStatusFilterChange={setStatusFilter}
        onRefresh={() => refetch()}
        isLoading={articlesLoading}
      />

      {/* Stats Cards */}
      <ArticlesStats articles={allArticles} />

      {/* Content */}
      <ArticlesContent
        articles={filteredArticles}
        loading={articlesLoading}
        searchTerm={searchTerm}
        statusFilter={statusFilter}
      />
    </motion.div>
  );
}

export default ArticlesPage;
