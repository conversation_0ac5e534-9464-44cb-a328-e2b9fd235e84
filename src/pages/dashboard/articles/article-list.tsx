import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Article } from '@/types/article';
import { Calendar, Eye, Plus } from 'lucide-react';

interface ArticleListProps {
  articles: Article[];
  loading?: boolean;
}

export const ArticleList: React.FC<ArticleListProps> = ({
  articles,
  loading,
}) => (
  <div className="space-y-6">
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h2 className="text-3xl font-bold text-foreground mb-1">Articles</h2>
        <p className="text-muted-foreground text-base">
          Manage your articles across all websites
        </p>
      </div>
      <Button className="w-full sm:w-auto">
        <Plus className="mr-2 h-4 w-4" />
        Create Article
      </Button>
    </div>

    <Card className="shadow-lg border-none bg-white dark:bg-zinc-900">
      <CardContent className="p-0">
        {loading ? (
          <div className="divide-y">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="p-6 animate-pulse flex gap-4">
                <div className="w-20 h-20 bg-muted rounded-lg flex-shrink-0"></div>
                <div className="flex-1">
                  <div className="h-5 bg-muted rounded w-2/3 mb-2"></div>
                  <div className="h-4 bg-muted rounded w-full mb-2"></div>
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="divide-y">
            {articles.map(article => (
              <div
                key={article.id}
                className="p-6 flex gap-4 hover:bg-muted/40 transition-colors cursor-pointer"
              >
                {/* Article Image */}
                <div className="w-20 h-20 bg-muted rounded-lg flex-shrink-0 overflow-hidden">
                  {article.featured_image ? (
                    <img
                      src={article.featured_image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={e => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                      No Image
                    </div>
                  )}
                </div>

                {/* Article Content */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-lg text-foreground truncate mb-1">
                    {article.title}
                  </h3>

                  {/* Description/Excerpt */}
                  {(article.excerpt || article.content) && (
                    <p className="text-muted-foreground text-sm line-clamp-2 mb-2">
                      {article.excerpt ||
                        (article.content
                          ? article.content
                              .replace(/<[^>]*>/g, '')
                              .substring(0, 150) + '...'
                          : '')}
                    </p>
                  )}

                  {/* Meta Information */}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    {article.published_at && (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>
                          {new Date(article.published_at).toLocaleDateString(
                            'en-US',
                            {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                            }
                          )}
                        </span>
                      </div>
                    )}

                    {article.views !== undefined && (
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        <span>{article.views.toLocaleString()} views</span>
                      </div>
                    )}

                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        article.status === 'published'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : article.status === 'draft'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      }`}
                    >
                      {article.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}

            {articles.length === 0 && !loading && (
              <div className="text-center py-16">
                <CardHeader>
                  <CardTitle>No articles yet</CardTitle>
                  <CardDescription>
                    Start creating content for your websites
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Your First Article
                  </Button>
                </CardContent>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  </div>
);
