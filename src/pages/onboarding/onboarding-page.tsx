import Header from '@/components/layout/header';
import SidebarProgressImproved from '@/components/layout/sidebar-progress-improved';
import AuthStep from '@/components/onboarding-steps/auth-step';
import CredentialStep from '@/components/onboarding-steps/credentials-step';
import PlanStep from '@/components/onboarding-steps/plan-step';
import PluginStep from '@/components/onboarding-steps/plugin-step';
import ReviewStep from '@/components/onboarding-steps/review-step';
import WebsiteStep from '@/components/onboarding-steps/website-step';
import { useOnboarding } from '@/hooks/use-onboarding';
import { useAuth } from '@/stores';
import {
  AuthStepData,
  CredentialsStepData,
  PlanStepData,
  PluginStepData,
  WebsiteStepData,
} from '@/types/onboarding';
import { getFilteredSteps, getPreviousStep } from '@/utils/onboarding-steps';

export default function OnboardingPage() {
  const { user } = useAuth();
  const {
    currentStep,
    onboardingState,
    completedSteps,
    goToNextStep,
    goToStep,
    updateStepData,
    handleCompleteOnboarding,
  } = useOnboarding();

  const filteredSteps = getFilteredSteps(!!user);

  const renderStep = () => {
    const prevStep = getPreviousStep(currentStep);
    const onBack = prevStep ? () => goToStep(prevStep) : undefined;

    switch (currentStep) {
      case 'auth':
        return (
          <AuthStep
            onNext={goToNextStep}
            onBack={onBack}
            initialValues={onboardingState.auth}
            onUpdate={(data: AuthStepData) => updateStepData('auth', data)}
          />
        );
      case 'website':
        return (
          <WebsiteStep
            onNext={goToNextStep}
            onBack={onBack}
            initialValues={onboardingState.website}
            onUpdate={(data: WebsiteStepData) =>
              updateStepData('website', data)
            }
          />
        );
      case 'plugin':
        return (
          <PluginStep
            onNext={goToNextStep}
            onBack={onBack}
            initialValues={onboardingState.plugin}
            onUpdate={(data: PluginStepData) => updateStepData('plugin', data)}
          />
        );
      case 'credentials':
        return (
          <CredentialStep
            onNext={goToNextStep}
            onBack={onBack}
            initialValues={onboardingState.credentials}
            onUpdate={(data: CredentialsStepData) =>
              updateStepData('credentials', data)
            }
          />
        );
      case 'plan':
        return (
          <PlanStep
            onNext={goToNextStep}
            onBack={onBack}
            initialValues={onboardingState.plan}
            onUpdate={(data: PlanStepData) => updateStepData('plan', data)}
          />
        );
      case 'review':
        return (
          <ReviewStep
            onNext={handleCompleteOnboarding}
            onboardingState={onboardingState}
            onEditStep={goToStep}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header />
      <main className="flex-grow flex">
        <div className="container mx-auto px-4 py-8 flex flex-col lg:flex-row gap-8">
          <div className="lg:w-[70%] ">{renderStep()}</div>
          <div className="lg:w-[30%]">
            <SidebarProgressImproved
              currentStep={currentStep}
              steps={filteredSteps}
              onSelectStep={goToStep}
              completedSteps={completedSteps}
            />
          </div>
        </div>
      </main>
    </div>
  );
}
