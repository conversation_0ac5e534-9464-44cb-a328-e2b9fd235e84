import { FormPasswordField, FormTextField } from '@/components/forms';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/stores';
import { ArrowLeft, Loader2, UserPlus } from 'lucide-react';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

export default function AuthPage() {
  const { signIn, resetPassword } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [forgotPassword, setForgotPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm({ defaultValues: { email: '', password: '' } });
  const resetForm = useForm({ defaultValues: { email: '' } });

  const handleSignIn = async (data: { email: string; password: string }) => {
    setLoading(true);
    setError(null);
    const { error } = await signIn(data.email, data.password);
    if (error) {
      setError(error.message);
      toast.error('Sign in failed: ' + error.message);
    } else {
      toast.success("You've been signed in successfully.");
      // The AuthRouteGuard will handle redirecting to the appropriate dashboard
    }
    setLoading(false);
  };

  const handleCreateAccount = () => {
    // Start the onboarding process for new users
    navigate('/onboarding?step=auth');
  };

  const handleResetPassword = async (data: { email: string }) => {
    setLoading(true);
    setError(null);
    const { error } = await resetPassword(data.email);
    if (error) {
      setError(error.message);
      toast.error('Reset password failed: ' + error.message);
    } else {
      toast.success('Check your email for a password reset link.');
      setForgotPassword(false);
    }
    setLoading(false);
  };

  if (forgotPassword) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <div className="w-full max-w-md space-y-6">
          {/* Header */}
          <div className="text-center space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-primary">
              SEO<span className="text-primary">45</span>
            </div>
            <div className="space-y-1">
              <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                Reset Password
              </h1>
              <p className="text-sm md:text-base text-muted-foreground">
                Enter your email address and we'll send you a reset link
              </p>
            </div>
          </div>

          <Card className="border-border shadow-lg">
            <CardContent className="p-6 space-y-4">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
                onClick={() => setForgotPassword(false)}
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Sign In
              </Button>

              <FormProvider {...resetForm}>
                <form
                  onSubmit={resetForm.handleSubmit(handleResetPassword)}
                  className="space-y-4"
                >
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription className="text-sm">
                        {error}
                      </AlertDescription>
                    </Alert>
                  )}
                  <FormTextField
                    name="email"
                    label="Email"
                    placeholder="Enter your email"
                    required
                  />
                  <Button
                    type="submit"
                    className="w-full h-11"
                    disabled={loading}
                  >
                    {loading && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Send Reset Link
                  </Button>
                </form>
              </FormProvider>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="text-3xl md:text-4xl font-bold text-primary">
            SEO<span className="text-primary">45</span>
          </div>
          <div className="space-y-1">
            <h1 className="text-2xl md:text-3xl font-bold text-foreground">
              Welcome Back
            </h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Sign in to your account to continue
            </p>
          </div>
        </div>

        <Card className="border-border shadow-lg">
          <CardContent className="p-6">
            <FormProvider {...form}>
              <form
                onSubmit={form.handleSubmit(handleSignIn)}
                className="space-y-4"
              >
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription className="text-sm">
                      {error}
                    </AlertDescription>
                  </Alert>
                )}
                <FormTextField
                  name="email"
                  label="Email"
                  placeholder="Enter your email"
                  required
                />
                <FormPasswordField
                  name="password"
                  label="Password"
                  placeholder="Enter your password"
                  required
                />
                <Button
                  type="submit"
                  className="w-full h-11"
                  disabled={loading}
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Sign In
                </Button>
                <Button
                  type="button"
                  variant="link"
                  className="w-full text-sm text-muted-foreground hover:text-primary"
                  onClick={() => setForgotPassword(true)}
                >
                  Forgot your password?
                </Button>
              </form>
            </FormProvider>
            {/* Divider */}
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-border" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-card px-2 text-muted-foreground">or</span>
              </div>
            </div>
            {/* Create Account Button */}
            <Button
              type="button"
              variant="outline"
              className="w-full h-11"
              onClick={handleCreateAccount}
            >
              <UserPlus className="mr-2 h-4 w-4" />
              Create New Account
            </Button>
            <p className="text-xs text-muted-foreground text-center mt-4">
              New to SEO45? Creating an account will guide you through our
              onboarding process.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
