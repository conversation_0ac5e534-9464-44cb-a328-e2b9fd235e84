import Header from '@/components/layout/header';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { clearOnboardingData } from '@/lib/onboarding';
import { CheckIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';

export default function SuccessPage() {
  const [, setLocation] = useLocation();
  const [progress, setProgress] = useState(0);
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    // Automatically increase progress over time
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          return 100;
        }
        return prev + 5;
      });
    }, 200);

    return () => {
      clearInterval(timer);
    };
  }, []);

  useEffect(() => {
    // Redirect to dashboard when progress reaches 100%
    if (progress === 100 && !isRedirecting) {
      const redirectTimer = setTimeout(() => {
        clearOnboardingData(); // Clear onboarding data once setup is complete
        setLocation('/dashboard');
      }, 1000);

      setIsRedirecting(true);
      return () => clearTimeout(redirectTimer);
    }
  }, [progress, setLocation, isRedirecting]);

  const handleGoDashboard = () => {
    clearOnboardingData(); // Clear onboarding data once setup is complete
    setLocation('/dashboard');
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow flex items-center justify-center">
        <div className="text-center p-8 max-w-md">
          <div className="mx-auto w-20 h-20 bg-gray-900 rounded-full flex items-center justify-center mb-6">
            <CheckIcon className="h-10 w-10 text-white" />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Congratulations, you have successfully connected your WordPress
            website with SEO45 AI.
          </h1>
          <p className="text-gray-600 mb-8">
            We are creating your workspace, so just relax
          </p>

          <Progress className="h-2.5 mb-6" value={progress} />

          <Button
            onClick={handleGoDashboard}
            className="mt-6"
            disabled={progress < 100}
          >
            {progress < 100
              ? 'Setting up your workspace...'
              : 'Go to Dashboard'}
          </Button>
        </div>
      </main>
    </div>
  );
}
