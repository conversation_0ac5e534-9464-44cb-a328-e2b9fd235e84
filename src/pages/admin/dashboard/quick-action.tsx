import type { QuickAction } from '@/types/admin/dashboard.types';
import { ArrowRight } from 'lucide-react';
import { Link } from 'wouter';

import { CardWrapper, SectionWrapper } from '@/components/common';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Globe, Package, Tag, Users } from 'lucide-react';
import React from 'react';

const quickActions: QuickAction[] = [
  {
    title: 'Add User',
    href: '/admin/users/new',
    icon: <Users className="h-5 w-5" />,
    description: 'Create a new user account',
    variant: 'blue',
  },
  {
    title: 'Add Plan',
    href: '/admin/plans/new',
    icon: <Package className="h-5 w-5" />,
    description: 'Create a new subscription plan',
    variant: 'green',
  },
  {
    title: 'Add Coupon',
    href: '/admin/coupons/new',
    icon: <Tag className="h-5 w-5" />,
    description: 'Create a discount coupon',
    variant: 'purple',
  },
  {
    title: 'Add Website',
    href: '/admin/websites/new',
    icon: <Globe className="h-5 w-5" />,
    description: 'Add a new website to track',
    variant: 'orange',
  },
];

const QuickActions = () => {
  return (
    <SectionWrapper spacing="md">
      <CardWrapper
        title="Quick Actions"
        description="Streamline your workflow with these common administrative tasks"
      >
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {quickActions.map(action => (
            <QuickActionButton key={action.title} {...action} />
          ))}
        </div>
      </CardWrapper>
    </SectionWrapper>
  );
};

export default QuickActions;

const QuickActionButton: React.FC<
  QuickAction & {
    description?: string;
    variant?: 'blue' | 'green' | 'purple' | 'orange' | 'default';
  }
> = ({ title, href, icon, description, variant = 'default' }) => {
  const getIconStyles = (variant: string) => {
    switch (variant) {
      case 'blue':
        return 'bg-blue-100 text-blue-600';
      case 'green':
        return 'bg-green-100 text-green-600';
      case 'purple':
        return 'bg-purple-100 text-purple-600';
      case 'orange':
        return 'bg-orange-100 text-orange-600';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <Link href={href}>
      <Card className="group cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-[1.02]">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className={cn('p-2 rounded-lg', getIconStyles(variant))}>
              {icon}
            </div>
            <ArrowRight className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>

          <div className="space-y-1">
            <h3 className="font-semibold text-sm">{title}</h3>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};
