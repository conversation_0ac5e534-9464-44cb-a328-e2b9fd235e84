import { Button } from '@/components/ui/button';
import {
  Card,
  Card<PERSON>ontent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Stat } from '@/types/admin/dashboard.types';
import { ArrowUpRight } from 'lucide-react';
import { Link } from 'wouter';

// StatCard Component
export const StatCard: React.FC<Stat & { loading: boolean }> = ({
  title,
  value,
  href,
  icon,
  color,
  description,
  loading,
}) => (
  <Card className="overflow-hidden hover:shadow-lg transition-shadow">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <div className={`rounded-md p-2 ${color}`}>{icon}</div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">
        {loading ? '...' : value.toLocaleString()}
      </div>
      <p className="text-xs text-muted-foreground">{description}</p>
    </CardContent>
    <CardFooter className="p-2">
      <Link href={href}>
        <Button variant="ghost" size="sm" className="w-full justify-between">
          View All
          <ArrowUpRight className="h-4 w-4" />
        </Button>
      </Link>
    </CardFooter>
  </Card>
);
