import { CardWrapper, SectionWrapper } from '@/components/common';
import { cn } from '@/lib/utils';
import type { SystemInfo } from '@/types/admin/dashboard.types';
import {
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Code,
  Database,
  Server,
} from 'lucide-react';
import React from 'react';

const SystemInfo = () => {
  const systemInfo: SystemInfo[] = [
    {
      label: 'Environment',
      value: 'Production',
      icon: <Server className="h-4 w-4" />,
      status: 'success',
      variant: 'green',
    },
    {
      label: 'Server Time',
      value: new Date().toLocaleString(),
      icon: <Clock className="h-4 w-4" />,
      status: 'info',
      variant: 'blue',
    },
    {
      label: 'Version',
      value: '1.0.0',
      icon: <Code className="h-4 w-4" />,
      status: 'info',
      variant: 'purple',
    },
    {
      label: 'Database',
      value: 'Supabase',
      icon: <Database className="h-4 w-4" />,
      status: 'success',
      variant: 'emerald',
    },
    {
      label: 'System Status',
      value: 'Operational',
      icon: <Activity className="h-4 w-4" />,
      status: 'success',
      variant: 'green',
    },
  ];

  return (
    <SectionWrapper spacing="md">
      <CardWrapper
        title="System Information"
        description="Real-time details about the current system status and configuration"
      >
        <div className="grid gap-3 sm:grid-cols-1 lg:grid-cols-2">
          {systemInfo.map(info => (
            <SystemInfoItem key={info.label} {...info} />
          ))}
        </div>
      </CardWrapper>
    </SectionWrapper>
  );
};

const SystemInfoItem: React.FC<
  SystemInfo & {
    icon?: React.ReactNode;
    status?: 'success' | 'warning' | 'error' | 'info';
    variant?: 'green' | 'blue' | 'purple' | 'emerald' | 'yellow' | 'red';
  }
> = ({ label, value, icon, status = 'info', variant = 'blue' }) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-3 w-3 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-500" />;
      default:
        return null;
    }
  };

  const getIconStyles = (variant: string) => {
    switch (variant) {
      case 'green':
        return 'bg-green-100 text-green-600';
      case 'blue':
        return 'bg-blue-100 text-blue-600';
      case 'purple':
        return 'bg-purple-100 text-purple-600';
      case 'emerald':
        return 'bg-emerald-100 text-emerald-600';
      case 'yellow':
        return 'bg-yellow-100 text-yellow-600';
      case 'red':
        return 'bg-red-100 text-red-600';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="group relative p-3 rounded-lg border transition-all duration-200 hover:shadow-sm">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          {icon && (
            <div className={cn('p-2 rounded-lg', getIconStyles(variant))}>
              {icon}
            </div>
          )}
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              {label}
            </span>
            <span className="text-sm font-semibold text-foreground mt-1">
              {value}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-1">{getStatusIcon()}</div>
      </div>
    </div>
  );
};

export default SystemInfo;
