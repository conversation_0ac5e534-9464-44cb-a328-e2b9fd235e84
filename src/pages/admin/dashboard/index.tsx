import { DashboardPageWrapper } from '@/components/common';
import { PageHeading } from '@/components/common/headings';
import { SectionWrapper } from '@/components/common/section-wrapper';
import { Card, CardContent } from '@/components/ui/card';
import { adminService } from '@/services/admin';
import type { Stat, StatsState } from '@/types/admin/dashboard.types';
import { motion } from 'framer-motion';
import {
  ActivityIcon,
  AlertCircle,
  BarChart3Icon,
  CreditCard,
  DollarSignIcon,
  Globe,
  Package,
  Receipt,
  Tag,
  TrendingUpIcon,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import QuickActions from './quick-action';
import { StatCard } from './stat-card';
import SystemInfo from './system-info';

// Main Component
const AdminDashboardPage: React.FC = () => {
  const [stats, setStats] = useState<StatsState>({
    users: 0,
    subscriptions: 0,
    coupons: 0,
    plans: 0,
    websites: 0,
    transactions: 0,
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const [users, subscriptions, coupons, plans] = await Promise.all([
          adminService.getAllUsers(),
          adminService.getAllSubscriptions(),
          adminService.getAllCoupons(),
          adminService.getAllPlans(),
        ]);

        setStats({
          users: users.length,
          subscriptions: subscriptions.length,
          coupons: coupons.length,
          plans: plans.length,
          websites: 67,
          transactions: 345,
        });
        setError(null);
      } catch (err) {
        setError('Failed to fetch dashboard data');
        console.error(err);
        setStats({
          users: 245,
          subscriptions: 128,
          plans: 4,
          coupons: 12,
          websites: 67,
          transactions: 345,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const dashboardStats: Stat[] = [
    {
      title: 'Users',
      value: stats.users,
      href: '/admin/users',
      icon: <Users className="h-4 w-4" />,
      color: 'bg-blue-500/20 text-blue-500',
      description: 'Total registered users',
    },
    {
      title: 'Subscriptions',
      value: stats.subscriptions,
      href: '/admin/subscriptions',
      icon: <CreditCard className="h-4 w-4" />,
      color: 'bg-green-500/20 text-green-500',
      description: 'Active subscriptions',
    },
    {
      title: 'Plans',
      value: stats.plans,
      href: '/admin/plans',
      icon: <Package className="h-4 w-4" />,
      color: 'bg-purple-500/20 text-purple-500',
      description: 'Available pricing plans',
    },
    {
      title: 'Coupons',
      value: stats.coupons,
      href: '/admin/coupons',
      icon: <Tag className="h-4 w-4" />,
      color: 'bg-yellow-500/20 text-yellow-500',
      description: 'Active discount coupons',
    },
    {
      title: 'Websites',
      value: stats.websites,
      href: '/admin/websites',
      icon: <Globe className="h-4 w-4" />,
      color: 'bg-pink-500/20 text-pink-500',
      description: 'Managed websites',
    },
    {
      title: 'Transactions',
      value: stats.transactions,
      href: '/admin/transactions',
      icon: <Receipt className="h-4 w-4" />,
      color: 'bg-indigo-500/20 text-indigo-500',
      description: 'Total payment transactions',
    },
  ];

  return (
    <motion.div initial="hidden" animate="visible" variants={containerVariants}>
      <DashboardPageWrapper>
        <motion.div variants={itemVariants}>
          <PageHeading
            title="Admin Dashboard"
            description="Monitor your platform's performance, manage users, and track key metrics."
          />
        </motion.div>

        {error && (
          <motion.div variants={itemVariants}>
            <SectionWrapper spacing="sm">
              <div className="bg-destructive/15 text-destructive px-6 py-4 rounded-lg flex items-center gap-3 border border-destructive/20">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">{error}</span>
              </div>
            </SectionWrapper>
          </motion.div>
        )}

        {/* Summary Cards */}
        <motion.div variants={itemVariants}>
          <SectionWrapper spacing="md">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
              <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200/50 dark:border-blue-800/30">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Total Revenue
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        $24,580
                      </p>
                    </div>
                    <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                      <DollarSignIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <TrendingUpIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-1" />
                    <span className="text-green-600 dark:text-green-400 font-medium">
                      +12%
                    </span>
                    <span className="text-muted-foreground ml-1">
                      from last month
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 border-green-200/50 dark:border-green-800/30">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Active Users
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {stats.users}
                      </p>
                    </div>
                    <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                      <ActivityIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <TrendingUpIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-1" />
                    <span className="text-green-600 dark:text-green-400 font-medium">
                      +8%
                    </span>
                    <span className="text-muted-foreground ml-1">
                      from last week
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/20 dark:to-purple-800/10 border-purple-200/50 dark:border-purple-800/30">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Subscriptions
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {stats.subscriptions}
                      </p>
                    </div>
                    <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                      <BarChart3Icon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <TrendingUpIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-1" />
                    <span className="text-green-600 dark:text-green-400 font-medium">
                      +15%
                    </span>
                    <span className="text-muted-foreground ml-1">
                      conversion rate
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-orange-50 to-orange-100/50 dark:from-orange-900/20 dark:to-orange-800/10 border-orange-200/50 dark:border-orange-800/30">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Websites
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {stats.websites}
                      </p>
                    </div>
                    <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                      <Globe className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <span className="text-orange-600 dark:text-orange-400 font-medium">
                      Active
                    </span>
                    <span className="text-muted-foreground ml-1">
                      and monitored
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </SectionWrapper>
        </motion.div>

        {/* Stats Cards */}
        <motion.div variants={itemVariants}>
          <SectionWrapper spacing="md">
            <h2 className="text-2xl font-bold text-foreground mb-6">
              Platform Statistics
            </h2>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {dashboardStats.map((stat, index) => (
                <motion.div
                  key={stat.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <StatCard {...stat} loading={loading} />
                </motion.div>
              ))}
            </div>
          </SectionWrapper>
        </motion.div>

        <motion.div variants={itemVariants}>
          <QuickActions />
        </motion.div>

        <motion.div variants={itemVariants}>
          <SystemInfo />
        </motion.div>
      </DashboardPageWrapper>
    </motion.div>
  );
};

export default AdminDashboardPage;
