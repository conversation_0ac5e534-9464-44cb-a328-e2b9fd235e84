import SubscriptionForm from '@/components/admin/subscriptions/subscription-form';
import SubscriptionTable from '@/components/admin/subscriptions/subscription-table';
import { AdaptiveWrapper } from '@/components/common/adaptive-wrapper';
import { EmptyState } from '@/components/common/empty-state';
import { PageHeading } from '@/components/common/headings';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SubscriptionUpdateData } from '@/lib/schemas/subscription-schema';
import { adminService } from '@/services/admin';
import { Subscription } from '@/supabase/types';
import {
  AlertCircle,
  AlertTriangle,
  Calendar,
  CreditCard,
  Loader2,
  TrendingUp,
  Users,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { DashboardPageWrapper } from '../../../components/common';

const AdminSubscriptionsPage: React.FC = () => {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState<
    Subscription | undefined
  >(undefined);
  const [savingSubscription, setSavingSubscription] = useState(false);

  const fetchSubscriptions = async () => {
    try {
      setLoading(true);
      const data = await adminService.getAllSubscriptions();
      setSubscriptions(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch subscriptions');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscriptions();
  }, []);

  const handleViewSubscription = (subscription: Subscription) => {
    setCurrentSubscription(subscription);
    setShowForm(false);
  };

  const handleEditSubscription = (subscription: Subscription) => {
    setCurrentSubscription(subscription);
    setShowForm(true);
  };

  const handleSaveSubscription = async (
    subscriptionData: SubscriptionUpdateData
  ) => {
    try {
      setSavingSubscription(true);
      if (currentSubscription) {
        // Update existing subscription
        const updatedSubscription = await adminService.updateSubscription(
          currentSubscription.id,
          subscriptionData
        );
        setSubscriptions(
          subscriptions.map(sub =>
            sub.id === currentSubscription.id ? updatedSubscription : sub
          )
        );
      }
      setShowForm(false);
      setCurrentSubscription(undefined);
    } catch (err) {
      setError('Failed to save subscription');
      console.error(err);
    } finally {
      setSavingSubscription(false);
    }
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setCurrentSubscription(undefined);
  };

  // Calculate stats
  const activeSubscriptionsCount = subscriptions.filter(
    sub => sub.status === 'active'
  ).length;
  const trialingSubscriptionsCount = subscriptions.filter(
    sub => sub.status === 'trialing'
  ).length;
  const expiringSoonCount = subscriptions.filter(sub => {
    if (!sub.current_period_end) return false;
    const expiryDate = new Date(sub.current_period_end);
    const weekFromNow = new Date();
    weekFromNow.setDate(weekFromNow.getDate() + 7);
    return expiryDate <= weekFromNow && expiryDate >= new Date();
  }).length;

  return (
    <DashboardPageWrapper>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <PageHeading
            title="Customer Subscriptions"
            description="Monitor and manage customer subscription statuses and billing."
          />
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Subscriptions
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{subscriptions.length}</div>
              <p className="text-xs text-muted-foreground">
                All customer subscriptions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {activeSubscriptionsCount}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active subscriptions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trialing</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {trialingSubscriptionsCount}
              </div>
              <p className="text-xs text-muted-foreground">In trial period</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Expiring Soon
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{expiringSoonCount}</div>
              <p className="text-xs text-muted-foreground">Within 7 days</p>
            </CardContent>
          </Card>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Expiring Soon Alert */}
        {expiringSoonCount > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {expiringSoonCount} subscription{expiringSoonCount > 1 ? 's' : ''}{' '}
              will expire within the next 7 days.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        {loading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading subscriptions...
            </CardContent>
          </Card>
        ) : subscriptions.length === 0 ? (
          <EmptyState
            icon={CreditCard}
            title="No Subscriptions Found"
            description="Customer subscriptions will appear here when customers subscribe to your plans."
            action={undefined}
          />
        ) : (
          <SubscriptionTable
            subscriptions={subscriptions}
            onView={handleViewSubscription}
            onEdit={handleEditSubscription}
          />
        )}

        {/* Adaptive Form Wrapper */}
        <AdaptiveWrapper
          isOpen={showForm}
          onClose={handleCloseForm}
          title="Update Subscription"
          description="Modify subscription status, billing period, and other settings."
          size="xl"
        >
          <SubscriptionForm
            subscription={currentSubscription}
            onSave={handleSaveSubscription}
            onCancel={handleCloseForm}
            loading={savingSubscription}
          />
        </AdaptiveWrapper>
      </div>
    </DashboardPageWrapper>
  );
};

export default AdminSubscriptionsPage;
