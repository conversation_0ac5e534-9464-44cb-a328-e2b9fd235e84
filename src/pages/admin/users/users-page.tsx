import UserForm from '@/components/admin/users/user-form';
import UserStats from '@/components/admin/users/user-stats';
import UserSubscriptionManager from '@/components/admin/users/user-subscription-manager';
import UserTable from '@/components/admin/users/user-table';
import {
  AdaptiveWrapper,
  DashboardPageWrapper,
  EmptyState,
  LoadingState,
  PageHeader,
  SectionWrapper,
} from '@/components/common';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useUsers } from '@/hooks/use-users';
import {
  CreateUserData,
  UpdateUserData,
  UserWithAuthData,
} from '@/types/admin/user-management';
import {
  AlertCircle,
  Filter,
  Plus,
  RefreshCw,
  Search,
  Users,
} from 'lucide-react';
import React, { useState } from 'react';
import { useLocation, useSearch } from 'wouter';

const AdminUsersPage: React.FC = () => {
  const { toast } = useToast();
  const [location, setLocation] = useLocation();
  const searchParams = new URLSearchParams(useSearch());

  // Enhanced users hook
  const {
    users,
    total,
    loading,
    error,
    filters,
    createUser,
    updateUser,
    deleteUser,
    resetUserPassword,
    toggleUserStatus,
    updateFilters,
    refreshUsers,
    clearError,
  } = useUsers();

  // Local state for UI
  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [selectedUser, setSelectedUser] = useState<UserWithAuthData | null>(
    null
  );
  const [showSubscriptionManager, setShowSubscriptionManager] = useState(false);

  // Get current state from URL params
  const isFormOpen =
    searchParams.get('action') === 'edit' ||
    searchParams.get('action') === 'add';
  const editUserId = searchParams.get('userId');
  const currentUser = editUserId
    ? users.find(u => u.id === editUserId)
    : undefined;

  // URL navigation helpers
  const handleAddUser = () => {
    const params = new URLSearchParams();
    params.set('action', 'add');
    setLocation(`${location}?${params.toString()}`);
  };

  const handleEditUser = (user: UserWithAuthData) => {
    const params = new URLSearchParams();
    params.set('action', 'edit');
    params.set('userId', user.id);
    setLocation(`${location}?${params.toString()}`);
  };

  const handleCloseForm = () => {
    setLocation(location.split('?')[0]); // Remove query params
  };

  // User management actions
  const handleDeleteUser = async (user: UserWithAuthData) => {
    if (
      window.confirm(
        `Are you sure you want to delete ${user.full_name || user.email}? This action cannot be undone.`
      )
    ) {
      try {
        await deleteUser(user.id);
        toast({
          title: 'User Deleted',
          description: `${user.full_name || user.email} has been deleted successfully.`,
        });
      } catch (error) {
        toast({
          title: 'Error',
          description:
            error instanceof Error ? error.message : 'Failed to delete user',
          variant: 'destructive',
        });
      }
    }
  };

  const handleManageSubscriptions = (user: UserWithAuthData) => {
    setSelectedUser(user);
    setShowSubscriptionManager(true);
  };

  const handleResetPassword = async (user: UserWithAuthData) => {
    const newPassword = window.prompt(
      `Enter a new password for ${user.full_name || user.email}:`,
      'TempPassword123!'
    );

    if (newPassword && newPassword.length >= 8) {
      try {
        await resetUserPassword(user.id, newPassword);
        toast({
          title: 'Password Reset',
          description: `Password has been reset for ${user.full_name || user.email}.`,
        });
      } catch (error) {
        toast({
          title: 'Error',
          description:
            error instanceof Error ? error.message : 'Failed to reset password',
          variant: 'destructive',
        });
      }
    } else if (newPassword !== null) {
      toast({
        title: 'Invalid Password',
        description: 'Password must be at least 8 characters long.',
        variant: 'destructive',
      });
    }
  };

  const handleToggleStatus = async (user: UserWithAuthData) => {
    const isActive = user.auth_user?.email_confirmed_at;
    const action = isActive ? 'suspend' : 'activate';

    if (
      window.confirm(
        `Are you sure you want to ${action} ${user.full_name || user.email}?`
      )
    ) {
      try {
        await toggleUserStatus(user.id, !!isActive);
        toast({
          title: `User ${action === 'suspend' ? 'Suspended' : 'Activated'}`,
          description: `${user.full_name || user.email} has been ${action}d successfully.`,
        });
      } catch (error) {
        toast({
          title: 'Error',
          description:
            error instanceof Error ? error.message : `Failed to ${action} user`,
          variant: 'destructive',
        });
      }
    }
  };

  // Form handlers
  const onSubmit = async (data: CreateUserData | UpdateUserData) => {
    try {
      if (currentUser) {
        // Update existing user
        await updateUser(currentUser.id, data as UpdateUserData);
        toast({
          title: 'User Updated',
          description: 'User information has been updated successfully.',
        });
      } else {
        // Create new user
        await createUser(data as CreateUserData);
        toast({
          title: 'User Created',
          description: 'New user has been created successfully.',
        });
      }

      handleCloseForm();
    } catch (err) {
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to save user',
        variant: 'destructive',
      });
    }
  };

  // Filter handlers
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    updateFilters({ ...filters, search: value || undefined });
  };

  const handleRoleFilter = (role: string) => {
    updateFilters({ ...filters, role: role === 'all' ? undefined : role });
  };

  const handleSubscriptionFilter = (hasSubscription: string) => {
    updateFilters({
      ...filters,
      has_subscription:
        hasSubscription === 'all' ? undefined : hasSubscription === 'true',
    });
  };

  const renderFilters = () => (
    <div className="flex flex-col sm:flex-row gap-4 mb-6">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search users by name, email, or username..."
          value={searchQuery}
          onChange={e => handleSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>

      <Select value={filters.role || 'all'} onValueChange={handleRoleFilter}>
        <SelectTrigger className="w-full sm:w-[180px]">
          <SelectValue placeholder="Filter by role" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Roles</SelectItem>
          <SelectItem value="user">Users</SelectItem>
          <SelectItem value="moderator">Moderators</SelectItem>
          <SelectItem value="admin">Admins</SelectItem>
        </SelectContent>
      </Select>

      <Select
        value={
          filters.has_subscription === undefined
            ? 'all'
            : filters.has_subscription.toString()
        }
        onValueChange={handleSubscriptionFilter}
      >
        <SelectTrigger className="w-full sm:w-[180px]">
          <SelectValue placeholder="Filter by subscription" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Users</SelectItem>
          <SelectItem value="true">With Subscription</SelectItem>
          <SelectItem value="false">Without Subscription</SelectItem>
        </SelectContent>
      </Select>

      <Button
        variant="outline"
        onClick={refreshUsers}
        className="flex items-center gap-2"
      >
        <RefreshCw className="h-4 w-4" />
        Refresh
      </Button>
    </div>
  );

  const renderContent = () => {
    if (loading) {
      return (
        <SectionWrapper spacing="md">
          <LoadingState message="Loading users..." />
        </SectionWrapper>
      );
    }

    if (
      users.length === 0 &&
      !filters.search &&
      !filters.role &&
      filters.has_subscription === undefined
    ) {
      return (
        <SectionWrapper spacing="md">
          <EmptyState
            icon={Users}
            title="No Users Found"
            description="There are no users in the system yet. Create your first user to get started."
            action={{
              label: 'Add First User',
              onClick: handleAddUser,
              variant: 'outline',
              icon: Plus,
            }}
          />
        </SectionWrapper>
      );
    }

    return (
      <SectionWrapper spacing="md">
        <div className="space-y-6">
          {/* User Statistics */}
          <UserStats users={users} total={total} />

          {renderFilters()}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">
                Showing {users.length} of {total} users
              </span>
            </div>
          </div>

          <UserTable
            users={users}
            onEdit={handleEditUser}
            onDelete={handleDeleteUser}
            onManageSubscriptions={handleManageSubscriptions}
            onResetPassword={handleResetPassword}
            onToggleStatus={handleToggleStatus}
          />
        </div>
      </SectionWrapper>
    );
  };

  const renderUserForm = () => (
    <UserForm
      user={currentUser}
      onSave={onSubmit}
      onCancel={handleCloseForm}
      isCreating={!currentUser}
    />
  );

  return (
    <DashboardPageWrapper>
      <PageHeader
        title="User Management"
        description="Manage users, their profiles, and subscription information across the platform."
        action={{
          label: 'Add New User',
          onClick: handleAddUser,
          icon: Plus,
        }}
      />

      {error && (
        <SectionWrapper spacing="sm">
          <div className="flex items-center justify-between p-4 border border-red-200 bg-red-50 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
              <span className="text-red-800">{error}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                clearError();
                refreshUsers();
              }}
            >
              Retry
            </Button>
          </div>
        </SectionWrapper>
      )}

      {renderContent()}

      {/* User Form Modal */}
      <AdaptiveWrapper
        isOpen={isFormOpen}
        onClose={handleCloseForm}
        title={currentUser ? 'Edit User Profile' : 'Create New User'}
        description={
          currentUser
            ? 'Update user profile information and settings.'
            : 'Create a new user account with profile and authentication details.'
        }
        size="lg"
      >
        {renderUserForm()}
      </AdaptiveWrapper>

      {/* Subscription Management Modal */}
      <UserSubscriptionManager
        user={selectedUser}
        isOpen={showSubscriptionManager}
        onClose={() => {
          setShowSubscriptionManager(false);
          setSelectedUser(null);
        }}
      />
    </DashboardPageWrapper>
  );
};

export default AdminUsersPage;
