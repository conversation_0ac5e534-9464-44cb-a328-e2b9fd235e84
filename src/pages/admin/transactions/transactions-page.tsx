import TransactionDetails from '@/components/admin/transactions/transaction-details';
import TransactionTable from '@/components/admin/transactions/transaction-table';
import { CardWrapper } from '@/components/common/card-wrapper';
import { PageHeading } from '@/components/common/headings';
import { SectionWrapper } from '@/components/common/section-wrapper';
import { Button } from '@/components/ui/button';
import { Transaction } from '@/supabase/types';
import { Loader2, RefreshCw } from 'lucide-react';
import { useState } from 'react';
import { DashboardPageWrapper } from '../../../components/common';

// Mock data for development - replace with actual API call
const mockTransactions: Transaction[] = [
  {
    id: 1,
    user_id: 'user-123',
    subscription_id: 1,
    amount: 2999,
    currency: 'USD',
    status: 'completed',
    payment_method: 'credit_card',
    payment_provider: 'stripe',
    payment_id: 'ch_123456',
    metadata: {
      card_brand: 'visa',
      last4: '4242',
      exp_month: 12,
      exp_year: 2024,
    },
    created_at: new Date().toISOString(),
    invoice_url: 'https://example.com/invoice/1',
    updated_at: null,
    coupon_id: null,
  },
  {
    id: 2,
    user_id: 'user-456',
    subscription_id: 2,
    amount: 4999,
    currency: 'USD',
    status: 'completed',
    payment_method: 'paypal',
    payment_provider: 'paypal',
    payment_id: 'PAYPAL123',
    metadata: {
      email: '<EMAIL>',
    },
    created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    invoice_url: 'https://example.com/invoice/2',
    updated_at: null,
    coupon_id: null,
  },
  {
    id: 3,
    user_id: 'user-789',
    subscription_id: 3,
    amount: 1999,
    currency: 'USD',
    status: 'failed',
    payment_method: 'credit_card',
    payment_provider: 'stripe',
    payment_id: 'ch_failed123',
    metadata: {
      card_brand: 'mastercard',
      last4: '1234',
      exp_month: 6,
      exp_year: 2023,
      error: 'Card expired',
    },
    created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    invoice_url: null,
    updated_at: null,
    coupon_id: null,
  },
];

// Profile data mapped by user_id
const mockProfiles: Record<string, { full_name: string; email: string }> = {
  'user-123': { full_name: 'John Doe', email: '<EMAIL>' },
  'user-456': { full_name: 'Jane Smith', email: '<EMAIL>' },
  'user-789': { full_name: 'Michael Brown', email: '<EMAIL>' },
};

const TransactionsPage = () => {
  const [transactions] = useState<Transaction[]>(mockTransactions);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);
  const [loading, setLoading] = useState(false);

  const handleView = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setDetailsOpen(true);
  };

  const handleViewReceipt = (transaction: Transaction) => {
    if (transaction.invoice_url) {
      window.open(transaction.invoice_url, '_blank');
    }
  };

  const handleRefresh = async () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => setLoading(false), 1000);
  };

  return (
    <DashboardPageWrapper>
      <div className="flex justify-between items-start">
        <PageHeading
          title="Transactions"
          description="View and manage all payment transactions in the system."
        />
        <Button
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          className="flex items-center gap-2"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      <SectionWrapper spacing="md">
        <CardWrapper
          title="All Transactions"
          description={`${transactions.length} transaction${transactions.length !== 1 ? 's' : ''} found`}
        >
          <TransactionTable
            transactions={transactions}
            onView={handleView}
            onViewReceipt={handleViewReceipt}
            profiles={mockProfiles}
          />
        </CardWrapper>
      </SectionWrapper>

      {selectedTransaction && (
        <TransactionDetails
          open={detailsOpen}
          onClose={() => setDetailsOpen(false)}
          transaction={selectedTransaction}
          profile={mockProfiles[selectedTransaction.user_id]}
        />
      )}
    </DashboardPageWrapper>
  );
};

export default TransactionsPage;
