import PlanForm from '@/components/admin/plans/plan-form';
import PlanTable from '@/components/admin/plans/plan-table';
import { AdaptiveWrapper } from '@/components/common/adaptive-wrapper';
import { EmptyState } from '@/components/common/empty-state';
import { PageHeading } from '@/components/common/headings';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PlanFormData } from '@/lib/schemas/plan-schema';
import { adminService } from '@/services/admin';
import { Plan } from '@/supabase/types';
import {
  AlertCircle,
  Loader2,
  Plus,
  Settings,
  TrendingUp,
  Users,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { DashboardPageWrapper } from '../../../components/common';

const AdminPlansPage: React.FC = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<Plan | undefined>(undefined);
  const [savingPlan, setSavingPlan] = useState(false);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const data = await adminService.getAllPlans();
      setPlans(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch plans');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  const handleEditPlan = (plan: Plan) => {
    setCurrentPlan(plan);
    setShowForm(true);
  };

  const handleDeletePlan = async (id: number) => {
    if (
      window.confirm(
        'Are you sure you want to delete this plan? This action cannot be undone.'
      )
    ) {
      try {
        await adminService.deletePlan(id);
        setPlans(plans.filter(plan => plan.id !== id));
      } catch (err) {
        setError('Failed to delete plan');
        console.error(err);
      }
    }
  };

  const handleSavePlan = async (planData: PlanFormData) => {
    try {
      setSavingPlan(true);
      if (currentPlan) {
        // Update existing plan
        const updatedPlan = await adminService.updatePlan(
          currentPlan.id,
          planData
        );
        setPlans(
          plans.map(plan => (plan.id === currentPlan.id ? updatedPlan : plan))
        );
      } else {
        // Create new plan
        const newPlan = await adminService.createPlan(planData);
        setPlans([newPlan, ...plans]);
      }
      setShowForm(false);
      setCurrentPlan(undefined);
    } catch (err) {
      setError('Failed to save plan');
      console.error(err);
    } finally {
      setSavingPlan(false);
    }
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setCurrentPlan(undefined);
  };

  const activePlansCount = plans.filter(plan => plan.is_active).length;
  const totalRevenue = plans.reduce((sum, plan) => sum + (plan.price || 0), 0);

  return (
    <DashboardPageWrapper>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <PageHeading
            title="Subscription Plans"
            description="Create and manage subscription plans for your service."
          />
          <Button
            onClick={() => {
              setCurrentPlan(undefined);
              setShowForm(true);
            }}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add New Plan
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Plans</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{plans.length}</div>
              <p className="text-xs text-muted-foreground">
                {activePlansCount} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Revenue Potential
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${totalRevenue.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">
                Combined monthly pricing
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Plans
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activePlansCount}</div>
              <p className="text-xs text-muted-foreground">
                Available to customers
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        {loading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading plans...
            </CardContent>
          </Card>
        ) : plans.length === 0 ? (
          <EmptyState
            icon={Settings}
            title="No Plans Found"
            description="Get started by creating your first subscription plan to begin offering your services to customers."
            action={{
              label: 'Create First Plan',
              onClick: () => {
                setCurrentPlan(undefined);
                setShowForm(true);
              },
              icon: Plus,
              variant: 'default',
            }}
          />
        ) : (
          <PlanTable
            plans={plans}
            onEdit={handleEditPlan}
            onDelete={handleDeletePlan}
          />
        )}

        {/* Adaptive Form Wrapper */}
        <AdaptiveWrapper
          isOpen={showForm}
          onClose={handleCloseForm}
          title={currentPlan ? 'Edit Plan' : 'Create New Plan'}
          description={
            currentPlan
              ? 'Update your subscription plan details.'
              : 'Set up a new subscription plan with features and pricing.'
          }
          size="xl"
        >
          <PlanForm
            plan={currentPlan}
            onSave={handleSavePlan}
            onCancel={handleCloseForm}
            loading={savingPlan}
          />
        </AdaptiveWrapper>
      </div>
    </DashboardPageWrapper>
  );
};

export default AdminPlansPage;
