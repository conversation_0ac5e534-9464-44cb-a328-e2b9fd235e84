import PlanSeeder from '@/components/admin/plan-seeder';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function AdminPlansPage() {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-2">
          Plan Administration
        </h2>
        <p className="text-muted-foreground">
          Manage subscription plans and pricing
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PlanSeeder />

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Use the Plan Seeder to populate your database with default
              subscription plans if you haven't set them up yet. This will
              create both monthly and annual plans with appropriate pricing and
              features.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
