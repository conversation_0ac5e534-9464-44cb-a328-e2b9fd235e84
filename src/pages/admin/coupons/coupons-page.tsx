import CouponForm from '@/components/admin/coupons/coupon-form';
import CouponTable from '@/components/admin/coupons/coupon-table';
import { AdaptiveWrapper } from '@/components/common/adaptive-wrapper';
import { EmptyState } from '@/components/common/empty-state';
import { PageHeading } from '@/components/common/headings';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CouponFormData } from '@/lib/schemas/coupon-schema';
import { adminService } from '@/services/admin';
import { Coupon } from '@/supabase/types';
import {
  AlertCircle,
  Calendar,
  Loader2,
  Percent,
  Plus,
  Ticket,
  Users,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { DashboardPageWrapper } from '../../../components/common';

const AdminCouponsPage: React.FC = () => {
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [currentCoupon, setCurrentCoupon] = useState<Coupon | undefined>(
    undefined
  );
  const [savingCoupon, setSavingCoupon] = useState(false);

  const fetchCoupons = async () => {
    try {
      setLoading(true);
      const data = await adminService.getAllCoupons();
      setCoupons(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch coupons');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCoupons();
  }, []);

  const handleEditCoupon = (coupon: Coupon) => {
    setCurrentCoupon(coupon);
    setShowForm(true);
  };

  const handleDeleteCoupon = async (id: number) => {
    if (
      window.confirm(
        'Are you sure you want to delete this coupon? This action cannot be undone.'
      )
    ) {
      try {
        await adminService.deleteCoupon(id);
        setCoupons(coupons.filter(coupon => coupon.id !== id));
      } catch (err) {
        setError('Failed to delete coupon');
        console.error(err);
      }
    }
  };

  const handleSaveCoupon = async (couponData: CouponFormData) => {
    try {
      setSavingCoupon(true);
      if (currentCoupon) {
        // Update existing coupon
        const updatedCoupon = await adminService.updateCoupon(
          currentCoupon.id,
          couponData
        );
        setCoupons(
          coupons.map(coupon =>
            coupon.id === currentCoupon.id ? updatedCoupon : coupon
          )
        );
      } else {
        // Create new coupon
        const newCoupon = await adminService.createCoupon(couponData);
        setCoupons([newCoupon, ...coupons]);
      }
      setShowForm(false);
      setCurrentCoupon(undefined);
    } catch (err) {
      setError('Failed to save coupon');
      console.error(err);
    } finally {
      setSavingCoupon(false);
    }
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setCurrentCoupon(undefined);
  };

  // Calculate stats
  const activeCouponsCount = coupons.filter(coupon => coupon.is_active).length;
  const totalUses = coupons.reduce(
    (sum, coupon) => sum + (coupon.current_uses || 0),
    0
  );
  const expiringSoonCount = coupons.filter(coupon => {
    if (!coupon.valid_until) return false;
    const expiryDate = new Date(coupon.valid_until);
    const weekFromNow = new Date();
    weekFromNow.setDate(weekFromNow.getDate() + 7);
    return expiryDate <= weekFromNow && expiryDate >= new Date();
  }).length;

  return (
    <DashboardPageWrapper>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <PageHeading
            title="Discount Coupons"
            description="Create and manage promotional coupons and discount codes."
          />
          <Button
            onClick={() => {
              setCurrentCoupon(undefined);
              setShowForm(true);
            }}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add New Coupon
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Coupons
              </CardTitle>
              <Ticket className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{coupons.length}</div>
              <p className="text-xs text-muted-foreground">
                {activeCouponsCount} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Uses</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalUses}</div>
              <p className="text-xs text-muted-foreground">
                Across all coupons
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Coupons
              </CardTitle>
              <Percent className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeCouponsCount}</div>
              <p className="text-xs text-muted-foreground">
                Available to customers
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Expiring Soon
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{expiringSoonCount}</div>
              <p className="text-xs text-muted-foreground">Within 7 days</p>
            </CardContent>
          </Card>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Expiring Soon Alert */}
        {expiringSoonCount > 0 && (
          <Alert>
            <Calendar className="h-4 w-4" />
            <AlertDescription>
              {expiringSoonCount} coupon{expiringSoonCount > 1 ? 's' : ''} will
              expire within the next 7 days.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        {loading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading coupons...
            </CardContent>
          </Card>
        ) : coupons.length === 0 ? (
          <EmptyState
            icon={Ticket}
            title="No Coupons Found"
            description="Start attracting customers by creating your first promotional coupon with discounts and special offers."
            action={{
              label: 'Create First Coupon',
              onClick: () => {
                setCurrentCoupon(undefined);
                setShowForm(true);
              },
              icon: Plus,
              variant: 'default',
            }}
          />
        ) : (
          <CouponTable
            coupons={coupons}
            onEdit={handleEditCoupon}
            onDelete={handleDeleteCoupon}
          />
        )}

        {/* Adaptive Form Wrapper */}
        <AdaptiveWrapper
          isOpen={showForm}
          onClose={handleCloseForm}
          title={currentCoupon ? 'Edit Coupon' : 'Create New Coupon'}
          description={
            currentCoupon
              ? 'Update your coupon details and settings.'
              : 'Set up a new promotional coupon with discount rules and validity.'
          }
          size="xl"
        >
          <CouponForm
            coupon={currentCoupon}
            onSave={handleSaveCoupon}
            onCancel={handleCloseForm}
            loading={savingCoupon}
          />
        </AdaptiveWrapper>
      </div>
    </DashboardPageWrapper>
  );
};

export default AdminCouponsPage;
