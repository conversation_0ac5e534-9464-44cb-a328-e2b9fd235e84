import WebsiteTable from '@/components/admin/websites/website-table';
import { CardWrapper } from '@/components/common/card-wrapper';
import { PageHeading } from '@/components/common/headings';
import { SectionWrapper } from '@/components/common/section-wrapper';
import { Button } from '@/components/ui/button';
import { Website } from '@/supabase/types';
import { Globe, Plus } from 'lucide-react';
import { useState } from 'react';
import { DashboardPageWrapper } from '../../../components/common';

// Mock data for development - replace with actual API call
const mockWebsites: Website[] = [
  {
    id: 1,
    domain_name: 'blog.example.com',
    website_url: 'https://blog.example.com',
    status: 'active',
    website_description: 'Main company blog with articles and updates',
    user_id: 'user-123',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    analytics_id: null,
    hosting_credentials: null,
    hosting_provider: 'vercel',
    ssl_enabled: true,
    ssl_expiry_date: null,
    website_images: null,
    website_niche: 'Technology',
    wordpress_id: null,
    wordpress_pass: null,
  },
  {
    id: 2,
    domain_name: 'store.example.com',
    website_url: 'https://store.example.com',
    status: 'active',
    website_description: 'Online store for selling products',
    user_id: 'user-123',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    analytics_id: null,
    hosting_credentials: null,
    hosting_provider: 'shopify',
    ssl_enabled: true,
    ssl_expiry_date: null,
    website_images: null,
    website_niche: 'E-commerce',
    wordpress_id: null,
    wordpress_pass: null,
  },
  {
    id: 3,
    domain_name: 'landing.example.com',
    website_url: 'https://landing.example.com',
    status: 'inactive',
    website_description: 'Landing page for marketing campaigns',
    user_id: 'user-123',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    analytics_id: null,
    hosting_credentials: null,
    hosting_provider: 'netlify',
    ssl_enabled: true,
    ssl_expiry_date: null,
    website_images: null,
    website_niche: 'Marketing',
    wordpress_id: null,
    wordpress_pass: null,
  },
];

const WebsitesPage = () => {
  const [websites] = useState<Website[]>(mockWebsites);

  const handleView = (website: Website) => {
    console.log('View website', website);
    // Implement view logic
  };

  const handleEdit = (website: Website) => {
    console.log('Edit website', website);
    // Implement edit logic
  };

  const handleDelete = (website: Website) => {
    console.log('Delete website', website);
    // Implement delete logic
  };

  const handleAddWebsite = () => {
    console.log('Add website');
    // Implement add logic
  };

  return (
    <DashboardPageWrapper>
      <div className="flex justify-between items-start">
        <PageHeading
          title="Websites"
          description="Manage and monitor all websites in the system."
        />
        <Button onClick={handleAddWebsite} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Website
        </Button>
      </div>

      <SectionWrapper spacing="md">
        {websites.length === 0 ? (
          <CardWrapper
            title="No Websites Found"
            description="There are no websites configured yet."
          >
            <div className="text-center py-6">
              <Globe className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground mb-4">
                Start by adding your first website.
              </p>
              <Button
                onClick={handleAddWebsite}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add First Website
              </Button>
            </div>
          </CardWrapper>
        ) : (
          <CardWrapper
            title="All Websites"
            description={`${websites.length} website${websites.length !== 1 ? 's' : ''} configured`}
          >
            <WebsiteTable
              websites={websites}
              onView={handleView}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          </CardWrapper>
        )}
      </SectionWrapper>
    </DashboardPageWrapper>
  );
};

export default WebsitesPage;
