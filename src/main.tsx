import { ThemeProvider } from 'next-themes';
import { createRoot } from 'react-dom/client';
import App from './App';
import './index.css';

// Import debug utilities in development
if (import.meta.env.DEV) {
  import('./utils/onboarding-debug.ts');
  import('./utils/test-onboarding-data.ts');
}

const rootElement = document.getElementById('root');
if (rootElement) {
  createRoot(rootElement).render(
    <ThemeProvider defaultTheme="light" attribute="class">
      <App />
    </ThemeProvider>
  );
}
