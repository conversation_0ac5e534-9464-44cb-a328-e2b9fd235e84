import { TooltipProvider } from '@/components/ui/tooltip';
import { AppRouter } from '@/routes';
import { useAuthStore } from '@/stores';
import { QueryClientProvider } from '@tanstack/react-query';
import { Suspense, useEffect } from 'react';
import { Toaster } from 'sonner';
import { queryClient } from './lib/query-client';

function App() {
  const initializeAuth = useAuthStore(state => state.initialize);

  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Suspense
          fallback={
            <div className="min-h-screen flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            </div>
          }
        >
          <AppRouter />
        </Suspense>
        <Toaster richColors />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
