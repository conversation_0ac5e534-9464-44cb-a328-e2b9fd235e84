'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import {
  ArrowRight,
  BarChart3,
  CheckCircle,
  Globe,
  Rocket,
  Sparkles,
  Star,
  TrendingUp,
  Zap,
} from 'lucide-react';

interface SEO45BannerProps {
  websiteCount: number;
  onAddWebsite: () => void;
}

export const SEO45Banner: React.FC<SEO45BannerProps> = ({
  websiteCount,
  onAddWebsite,
}) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  const floatingVariants = {
    animate: {
      y: [0, -10, 0],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 rounded-3xl"
    >
      {/* Enhanced Background Pattern */}
      {/* <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.08"%3E%3Ccircle cx="30" cy="30" r="1.5"/%3E%3Ccircle cx="10" cy="10" r="0.5"/%3E%3Ccircle cx="50" cy="50" r="0.5"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40" /> */}

      {/* Animated Gradient Overlays */}
      <motion.div
        animate={{
          x: [0, 20, 0],
          y: [0, -20, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-full blur-3xl transform translate-x-48 -translate-y-48"
      />
      <motion.div
        animate={{
          x: [0, -20, 0],
          y: [0, 20, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-cyan-400/20 to-blue-500/20 rounded-full blur-3xl transform -translate-x-48 translate-y-48"
      />

      {/* Additional floating elements */}
      <motion.div
        variants={floatingVariants}
        animate="animate"
        className="absolute top-20 left-20 w-3 h-3 bg-blue-400/30 rounded-full blur-sm"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        transition={{ delay: 1 }}
        className="absolute top-40 right-32 w-2 h-2 bg-purple-400/40 rounded-full blur-sm"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        transition={{ delay: 2 }}
        className="absolute bottom-32 left-32 w-4 h-4 bg-cyan-400/20 rounded-full blur-sm"
      />

      <CardContent className="relative px-8 py-12 md:p-12 lg:px-16 lg:py-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            {/* Brand Section */}
            <motion.div variants={itemVariants} className="space-y-6">
              <div className="flex items-center gap-4">
                <motion.div
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  className="relative"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-2xl blur-md opacity-60" />
                  <div className="relative p-4 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20">
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: 'linear',
                      }}
                    >
                      <Sparkles className="h-10 w-10 text-white" />
                    </motion.div>
                  </div>
                </motion.div>
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <motion.h1
                      variants={itemVariants}
                      className="text-5xl md:text-6xl lg:text-7xl font-black text-white tracking-tight"
                    >
                      SEO45
                    </motion.h1>
                    <motion.div variants={itemVariants} className="flex">
                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.1 * i, duration: 0.3 }}
                          whileHover={{ scale: 1.2 }}
                        >
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        </motion.div>
                      ))}
                    </motion.div>
                  </div>
                  <motion.div variants={itemVariants}>
                    <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0 font-semibold px-4 py-1">
                      <Rocket className="h-3 w-3 mr-1" />
                      Next-Gen AI Platform
                    </Badge>
                  </motion.div>
                </div>
              </div>

              <motion.div variants={itemVariants} className="space-y-4">
                <h2 className="text-2xl md:text-3xl font-bold text-white leading-tight">
                  Revolutionary AI-Powered SEO & Content Automation
                </h2>
                <p className="text-white/80 text-lg leading-relaxed max-w-xl">
                  Experience the future of digital marketing with our
                  cutting-edge AI that creates, optimizes, and manages your
                  content while you sleep.
                </p>
              </motion.div>
            </motion.div>

            {/* Feature Highlights */}
            <motion.div variants={itemVariants} className="space-y-4">
              {[
                {
                  icon: Zap,
                  title: 'Lightning-Fast Content Generation',
                  desc: 'AI creates optimized content in seconds',
                  gradient: 'from-blue-500 to-cyan-400',
                },
                {
                  icon: TrendingUp,
                  title: 'Advanced SEO Intelligence',
                  desc: 'Automatic ranking optimization',
                  gradient: 'from-purple-500 to-pink-400',
                },
                {
                  icon: BarChart3,
                  title: 'Real-Time Analytics',
                  desc: 'Track performance and growth',
                  gradient: 'from-emerald-500 to-green-400',
                },
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="flex items-center gap-4 p-4 bg-white/5 backdrop-blur-xl rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer group"
                >
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                    className={`p-3 bg-gradient-to-r ${feature.gradient} rounded-xl shadow-lg`}
                  >
                    <feature.icon className="h-6 w-6 text-white" />
                  </motion.div>
                  <div className="flex-1">
                    <div className="font-bold text-white group-hover:text-blue-200 transition-colors">
                      {feature.title}
                    </div>
                    <div className="text-white/70 text-sm">{feature.desc}</div>
                  </div>
                  <motion.div
                    initial={{ x: 0 }}
                    whileHover={{ x: 5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <ArrowRight className="h-5 w-5 text-white/40 group-hover:text-white/70 transition-colors" />
                  </motion.div>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Right Dashboard Card */}
          <motion.div variants={itemVariants} className="flex flex-col gap-8">
            {/* Stats Card */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              <Card className="bg-white/10 backdrop-blur-xl border-white/20 overflow-hidden shadow-2xl">
                <CardContent className="p-8">
                  <div className="text-center space-y-6">
                    <motion.div
                      variants={floatingVariants}
                      animate="animate"
                      className="relative"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-md opacity-30" />
                      <div className="relative p-4 bg-white/10 rounded-full inline-block">
                        <motion.div
                          animate={{ rotate: [0, 360] }}
                          transition={{
                            duration: 30,
                            repeat: Infinity,
                            ease: 'linear',
                          }}
                        >
                          <Globe className="h-12 w-12 text-white" />
                        </motion.div>
                      </div>
                    </motion.div>

                    <div className="space-y-2">
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{
                          type: 'spring',
                          stiffness: 200,
                          delay: 0.5,
                        }}
                        className="text-6xl md:text-7xl font-black bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent"
                      >
                        {websiteCount}
                      </motion.div>
                      <div className="text-white/80 font-semibold">
                        Active Website{websiteCount !== 1 ? 's' : ''}
                      </div>
                    </div>

                    {websiteCount > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8 }}
                        className="p-4 bg-gradient-to-r from-emerald-500/20 to-green-500/20 rounded-2xl border border-emerald-400/20"
                      >
                        <div className="text-emerald-200 font-bold text-sm flex items-center justify-center gap-2">
                          <motion.span
                            animate={{ rotate: [0, 15, -15, 0] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            🚀
                          </motion.span>
                          Performance Growing Daily
                          <CheckCircle className="h-4 w-4" />
                        </div>
                      </motion.div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Action Buttons */}
            <motion.div variants={itemVariants} className="space-y-4">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  onClick={onAddWebsite}
                  size="lg"
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 rounded-2xl py-6 text-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 group"
                >
                  <Globe className="h-6 w-6 mr-3 group-hover:rotate-12 transition-transform" />
                  Add New Website
                  <motion.div
                    initial={{ x: 0 }}
                    whileHover={{ x: 5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <ArrowRight className="h-5 w-5 ml-3" />
                  </motion.div>
                </Button>
              </motion.div>

              <motion.div
                variants={itemVariants}
                className="grid grid-cols-3 gap-3"
              >
                {[
                  { value: '99.9%', label: 'Uptime' },
                  { value: '24/7', label: 'Monitoring' },
                  { value: 'AI', label: 'Powered' },
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    whileHover={{ scale: 1.05, y: -2 }}
                    className="text-center p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-300 cursor-pointer border border-white/10"
                  >
                    <div className="text-2xl font-bold text-white">
                      {stat.value}
                    </div>
                    <div className="text-xs text-white/60">{stat.label}</div>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </CardContent>
    </motion.div>
  );
};
