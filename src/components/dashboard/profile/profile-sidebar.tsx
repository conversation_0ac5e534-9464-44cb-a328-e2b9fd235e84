'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Profile } from '@/supabase/types';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { motion } from 'framer-motion';
import { Calendar, CheckCircle, Shield, User } from 'lucide-react';

interface ProfileSidebarProps {
  user: SupabaseUser | null;
  profile: Profile | null;
  avatarPreview: string | null;
  onAvatarUpload: () => void;
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export function ProfileSidebar({
  user,
  profile,
  avatarPreview,
}: ProfileSidebarProps) {
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      <motion.div variants={itemVariants} className="space-y-6">
        {/* User Info Card */}
        <Card className="border shadow-sm bg-gradient-to-br from-primary/20 to-primary/10 backdrop-blur-xl  border-primary/50 ">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <div className="relative mx-auto w-20 h-20">
                <div className="w-full h-full rounded-full bg-muted flex items-center justify-center overflow-hidden border-2 border-border">
                  {avatarPreview || profile?.avatar_url ? (
                    <img
                      src={avatarPreview || profile?.avatar_url || ''}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="h-10 w-10 text-muted-foreground" />
                  )}
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                  <CheckCircle className="h-3 w-3 text-white" />
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-foreground">
                  {profile?.full_name || 'User'}
                </h3>
                <p className="text-sm text-muted-foreground">{user?.email}</p>
                <Badge variant="secondary" className="mt-2">
                  <Shield className="h-3 w-3 mr-1" />
                  Verified
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Stats */}
        <Card className="border shadow-sm">
          <CardHeader>
            <CardTitle className="text-base font-semibold flex items-center gap-2">
              <Calendar className="h-4 w-4 text-primary" />
              Account Stats
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-muted-foreground">
                Member Since
              </span>
              <span className="text-sm font-medium text-foreground">
                {formatDate(user?.created_at)}
              </span>
            </div>
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-muted-foreground">
                Last Updated
              </span>
              <span className="text-sm font-medium text-foreground">
                {formatDate(profile?.updated_at)}
              </span>
            </div>
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-muted-foreground">
                Profile Status
              </span>
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                Active
              </Badge>
            </div>
            <div className="flex items-center justify-between py-2">
              <span className="text-sm text-muted-foreground">
                Email Verified
              </span>
              <Badge
                variant="outline"
                className="bg-blue-50 text-blue-700 border-blue-200"
              >
                {user?.email_confirmed_at ? 'Verified' : 'Pending'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Account Security */}
        <Card className="bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-xl border border-primary/20 shadow-xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-base font-semibold flex items-center gap-2">
              <div className="p-1.5 bg-gradient-to-br from-red-500/20 to-red-500/10 rounded-lg">
                <Shield className="h-4 w-4 text-red-500" />
              </div>
              Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  Two-Factor Auth
                </span>
                <Badge
                  variant="outline"
                  className="bg-yellow-50 text-yellow-700 border-yellow-200"
                >
                  Disabled
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  Login Sessions
                </span>
                <span className="text-sm font-medium text-foreground">
                  1 Active
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  Last Login
                </span>
                <span className="text-sm font-medium text-foreground">
                  {formatDate(user?.last_sign_in_at)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
