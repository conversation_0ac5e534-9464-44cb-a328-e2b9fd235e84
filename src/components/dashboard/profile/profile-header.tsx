'use client';

import { motion } from 'framer-motion';

interface ProfileHeaderProps {
  title?: string;
  description?: string;
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export function ProfileHeader({
  title = 'Profile Settings',
  description = 'Manage your account settings and preferences with our enhanced interface.',
}: ProfileHeaderProps) {
  return (
    <motion.div variants={itemVariants}>
      <h1 className="text-3xl font-bold text-foreground mb-2">{title}</h1>
      <p className="text-muted-foreground text-lg">{description}</p>
    </motion.div>
  );
}
