'use client';

import { Article } from '@/types/article';
import { motion } from 'framer-motion';
import { ArticleCardsView } from './article-cards-view';

interface ArticlesContentProps {
  articles: Article[];
  loading: boolean;
  searchTerm: string;
  statusFilter: string;
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export function ArticlesContent({
  articles,
  loading,
  searchTerm,
  statusFilter,
}: ArticlesContentProps) {
  return (
    <motion.div variants={itemVariants}>
      <ArticleCardsView
        articles={articles}
        loading={loading}
        searchTerm={searchTerm}
        statusFilter={statusFilter}
      />
    </motion.div>
  );
}
