'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Article } from '@/types/article';
import { motion } from 'framer-motion';
import { Calendar, CheckCircle, FileTextIcon } from 'lucide-react';

interface ArticlesStatsProps {
  articles: Article[];
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export function ArticlesStats({ articles }: ArticlesStatsProps) {
  // Calculate basic stats
  const publishedCount = articles.filter(a => a.status === 'published').length;
  const draftCount = articles.filter(a => a.status === 'draft').length;

  return (
    <motion.div variants={itemVariants}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Total Articles
                </p>
                <p className="text-xl font-bold text-foreground">
                  {articles.length}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full">
                <FileTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm">
              <span className="text-green-600 dark:text-green-400 font-medium">
                {publishedCount} published
              </span>
              <span className="text-muted-foreground ml-1">
                • {draftCount} drafts
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Content Status
                </p>
                <p className="text-xl font-bold text-foreground">Active</p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm">
              <span className="text-green-600 dark:text-green-400 font-medium">
                Auto-publishing enabled
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Latest Update
                </p>
                <p className="text-xl font-bold text-foreground">Today</p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-full">
                <Calendar className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm">
              <span className="text-purple-600 dark:text-purple-400 font-medium">
                Content synchronized
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </motion.div>
  );
}
