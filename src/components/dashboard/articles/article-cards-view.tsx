'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Article } from '@/types/article';
import { Calendar, ContactIcon, Eye } from 'lucide-react';

interface ArticleCardsViewProps {
  articles: Article[];
  loading?: boolean;
  searchTerm?: string;
  statusFilter?: string;
}

export function ArticleCardsView({
  articles,
  loading,
  searchTerm,
  statusFilter,
}: ArticleCardsViewProps) {
  if (loading) {
    return (
      <Card className="border shadow-sm">
        <CardContent className="p-0">
          <div className="divide-y">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="p-6 animate-pulse flex gap-4">
                <div className="w-20 h-20 bg-muted rounded-lg flex-shrink-0"></div>
                <div className="flex-1">
                  <div className="h-5 bg-muted rounded w-2/3 mb-2"></div>
                  <div className="h-4 bg-muted rounded w-full mb-2"></div>
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/4"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (articles.length === 0) {
    return (
      <Card className="border shadow-sm">
        <CardContent className="text-center py-16">
          <CardHeader>
            <Calendar className="h-12 w-12 text-muted-foreground mb-4 mx-auto" />
            <CardTitle>No articles found</CardTitle>
            <CardDescription>
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search or filter criteria'
                : 'Waiting for articles to be generated...'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button>
              <ContactIcon className="h-4 w-4" />
              Contact Support
            </Button>
          </CardContent>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border shadow-sm">
      <CardContent className="p-0">
        <div className="divide-y">
          {articles.map(article => (
            <div
              key={article.id}
              className="p-6 flex gap-4 hover:bg-muted/40 transition-colors cursor-pointer"
            >
              {/* Article Image */}
              <div className="w-20 h-20 bg-muted rounded-lg flex-shrink-0 overflow-hidden">
                {article.featured_image ? (
                  <img
                    src={article.featured_image}
                    alt={article.title}
                    className="w-full h-full object-cover"
                    onError={e => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                    No Image
                  </div>
                )}
              </div>

              {/* Article Content */}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg text-foreground truncate mb-1">
                  {article.title}
                </h3>

                {/* Description/Excerpt */}
                {(article.excerpt || article.content) && (
                  <p className="text-muted-foreground text-sm line-clamp-2 mb-2">
                    {article.excerpt ||
                      (article.content
                        ? article.content
                            .replace(/<[^>]*>/g, '')
                            .substring(0, 150) + '...'
                        : '')}
                  </p>
                )}

                {/* Meta Information */}
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  {article.published_at && (
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      <span>
                        {new Date(article.published_at).toLocaleDateString(
                          'en-US',
                          {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                          }
                        )}
                      </span>
                    </div>
                  )}

                  {article.views !== undefined && (
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      <span>{article.views.toLocaleString()} views</span>
                    </div>
                  )}

                  {article.status && (
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        article.status === 'published'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : article.status === 'draft'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      }`}
                    >
                      {article.status}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
