import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { BarChart3, Crown, FileText, Globe, UserIcon } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

interface DashboardSidebarProps {
  className?: string;
}

const navigationItems = [
  {
    id: 'overview',
    label: 'Overview',
    icon: BarChart3,
    path: '/dashboard',
  },
  {
    id: 'websites',
    label: 'Websites',
    icon: Globe,
    path: '/dashboard/websites',
  },
  {
    id: 'articles',
    label: 'Articles',
    icon: FileText,
    path: '/dashboard/articles',
  },

  {
    id: 'subscriptions',
    label: 'Subscriptions',
    icon: Crown,
    path: '/dashboard/subscriptions',
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: UserIcon,
    path: '/dashboard/profile',
  },
];

export function DashboardSidebar({ className }: DashboardSidebarProps) {
  const location = useLocation();
  const navigate = useNavigate();

  const isActive = (path: string) => {
    // Handle /dashboard and /dashboard/overview as the same
    // if (
    //   path === '/dashboard' &&
    //   (location === '/dashboard' || location === '/dashboard')
    // ) {
    //   return true;
    // }
    return location.pathname === path;
  };

  return (
    <aside className={cn('bg-card border-r border-border', className)}>
      <nav className="p-4 space-y-2">
        {navigationItems.map(item => {
          const Icon = item.icon;
          return (
            <Button
              key={item.id}
              variant={isActive(item.path) ? 'default' : 'ghost'}
              className="w-full justify-start"
              onClick={() => navigate(item.path)}
            >
              <Icon className="mr-2 h-4 w-4" />
              {item.label}
            </Button>
          );
        })}
      </nav>
    </aside>
  );
}
