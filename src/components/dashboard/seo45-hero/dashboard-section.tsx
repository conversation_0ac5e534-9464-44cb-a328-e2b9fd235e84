'use client';

import { But<PERSON> } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { ArrowRight, Brain, Globe } from 'lucide-react';
import { AdditionalStats } from './additional-stats';
import { StatsCard } from './stats-card';

interface DashboardSectionProps {
  websiteCount: number;
  onAddWebsite: () => void;
}

export const DashboardSection: React.FC<DashboardSectionProps> = ({
  websiteCount,
  onAddWebsite,
}) => {
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.div variants={itemVariants} className="flex flex-col gap-6">
      {/* Main Stats Card - Website Count */}
      <StatsCard
        icon={Globe}
        count={websiteCount}
        label="Active Websites"
        gradient="from-primary to-primary/80"
        isMain={true}
      />

      {/* Additional Stats Row */}
      <AdditionalStats />

      {/* Enhanced AI Powered Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="p-5 bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 backdrop-blur-xl rounded-2xl border border-primary/20 dark:border-primary/30 hover:border-primary/40 hover:bg-primary/15 dark:hover:bg-primary/25 transition-all duration-300 hover:scale-105 hover:shadow-lg cursor-pointer group"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="p-2 bg-primary/10 dark:bg-primary/20 rounded-lg group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-colors duration-300"
            >
              <Brain className="h-5 w-5 text-primary group-hover:text-primary/80 transition-colors duration-300" />
            </motion.div>
            <div>
              <div className="font-semibold text-slate-800 dark:text-slate-200 group-hover:text-primary transition-colors duration-300">
                AI Intelligence
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Active & Learning
              </div>
            </div>
          </div>
          <motion.span
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-3 h-3 bg-green-500 rounded-full shadow-lg"
          />
        </div>
      </motion.div>

      {/* Enhanced Action Button */}
      <div className="space-y-3">
        <Button
          onClick={onAddWebsite}
          size="lg"
          className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 dark:from-primary dark:to-primary/80 dark:hover:from-primary/90 dark:hover:to-primary/70 text-white border-0 rounded-2xl py-6 text-lg font-bold shadow-2xl hover:shadow-primary/25 dark:hover:shadow-primary/30 transition-all duration-300 hover:scale-105 group"
        >
          <Globe className="h-6 w-6 mr-3 group-hover:rotate-12 transition-transform duration-300" />
          Add New Website
          <ArrowRight className="h-5 w-5 ml-3 group-hover:translate-x-1 transition-transform duration-300" />
        </Button>
      </div>
    </motion.div>
  );
};
