'use client';

import { Headphones, Users, Zap } from 'lucide-react';

interface StatItemProps {
  icon: React.ComponentType<{ className?: string }>;
  value: string;
  label: string;
}

const StatItem: React.FC<StatItemProps> = ({ icon: Icon, value, label }) => (
  <div className="group p-3 bg-white/70 dark:bg-slate-800/70 backdrop-blur-xl rounded-xl border border-primary/20 dark:border-primary/30 hover:border-primary/40 hover:scale-105 transition-all duration-300 cursor-pointer">
    <div className="text-center space-y-1">
      <Icon className="h-5 w-5 text-primary mx-auto group-hover:scale-110 transition-transform duration-300" />
      <div className="text-lg font-bold text-slate-800 dark:text-slate-200">
        {value}
      </div>
      <div className="text-xs text-slate-600 dark:text-slate-400">{label}</div>
    </div>
  </div>
);

export const AdditionalStats: React.FC = () => {
  const stats = [
    {
      icon: Headphones,
      value: '24/7',
      label: 'Support',
    },
    {
      icon: Users,
      value: '15K+',
      label: 'Users',
    },
    {
      icon: Zap,
      value: '99.9%',
      label: 'Uptime',
    },
  ];

  return (
    <div className="grid grid-cols-3 gap-3">
      {stats.map((stat, index) => (
        <StatItem
          key={index}
          icon={stat.icon}
          value={stat.value}
          label={stat.label}
        />
      ))}
    </div>
  );
};
