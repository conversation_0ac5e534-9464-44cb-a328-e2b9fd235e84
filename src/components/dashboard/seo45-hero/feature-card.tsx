'use client';

import { motion } from 'framer-motion';
import { ArrowRight, LucideIcon } from 'lucide-react';

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  gradient: string;
  delay?: number;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  icon: Icon,
  title,
  description,
  gradient,
  delay = 0,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay, duration: 0.5 }}
      className="flex items-center gap-4 p-4 bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-[#338bff]/10 dark:border-[#338bff]/20 hover:border-[#338bff]/30 dark:hover:border-[#338bff]/40 hover:bg-white/80 dark:hover:bg-slate-800/80 hover:shadow-lg hover:scale-105 hover:translate-x-2 transition-all duration-300 cursor-pointer group"
    >
      <div
        className={`p-3 bg-gradient-to-r ${gradient} rounded-xl shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-300`}
      >
        <Icon className="h-5 w-5 text-white" />
      </div>{' '}
      <div className="flex-1 space-y-1">
        <div className="font-bold text-slate-800 dark:text-slate-200 group-hover:text-[#338bff] transition-colors duration-300">
          {title}
        </div>
        <div className="text-slate-600 dark:text-slate-400 text-sm group-hover:text-slate-700 dark:group-hover:text-slate-300 transition-colors duration-300">
          {description}
        </div>
      </div>
      <ArrowRight className="h-4 w-4 text-slate-400 dark:text-slate-500 group-hover:text-[#338bff] group-hover:translate-x-1 transition-all duration-300" />
    </motion.div>
  );
};
