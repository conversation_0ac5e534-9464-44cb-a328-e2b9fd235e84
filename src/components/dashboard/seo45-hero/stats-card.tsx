'use client';

import { Card, CardContent } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  icon: LucideIcon;
  count: string | number;
  label: string;
  gradient: string;
  isMain?: boolean;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  icon: Icon,
  count,
  label,
  gradient,
  isMain = false,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: isMain ? 0.2 : 0.1, duration: 0.5 }}
      whileHover={{ y: -4 }}
      className="group cursor-pointer"
    >
      <Card className="relative overflow-hidden bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm border border-blue-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-300">
        {/* Dot pattern background */}
        <div className="absolute inset-0 opacity-30 dark:opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgb(148 163 184) 1px, transparent 0)`,
              backgroundSize: '20px 20px',
            }}
          />
        </div>

        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/30 dark:from-blue-950/20 dark:via-transparent dark:to-purple-950/20" />

        <CardContent className="relative text-center space-y-6 p-8">
          {/* Icon section */}
          <motion.div
            animate={{
              y: [0, -6, 0],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
            className="inline-block"
          >
            <div className="relative p-4 bg-gradient-to-br from-blue-500/10 to-purple-500/10 dark:from-blue-400/20 dark:to-purple-400/20 rounded-2xl border border-blue-200/50 dark:border-blue-400/30 group-hover:border-blue-300 dark:group-hover:border-blue-400 group-hover:scale-105 transition-all duration-300">
              <Icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </motion.div>

          {/* Stats content */}
          <div className="space-y-3">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                type: 'spring',
                stiffness: 200,
                delay: isMain ? 0.4 : 0.3,
              }}
              className={`${isMain ? 'text-6xl md:text-7xl' : 'text-4xl'} font-bold bg-gradient-to-r ${gradient} bg-clip-text text-transparent`}
            >
              {count}
            </motion.div>
            <div className="text-slate-700 dark:text-slate-300 font-semibold text-lg">
              {label}
            </div>
          </div>

          {/* Active status indicator */}
          {isMain && typeof count === 'number' && count > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-green-500/10 border border-green-400/20 rounded-full"
            >
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-2 h-2 bg-green-500 rounded-full"
              />
              <span className="text-green-600 dark:text-green-400 text-sm font-medium">
                Active Websites
              </span>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};
