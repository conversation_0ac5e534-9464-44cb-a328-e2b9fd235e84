import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { PlusIcon } from 'lucide-react';
import { useState } from 'react';

type Topic = {
  id: number;
  title: string;
};

export default function SuggestedTopics() {
  const [topics] = useState<Topic[]>([
    { id: 1, title: 'Voice Search Optimization' },
    { id: 2, title: 'Core Web Vitals Guide' },
    { id: 3, title: 'E-A-T Principles for Content' },
  ]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Suggested Topics</CardTitle>
        <p className="text-muted-foreground text-sm">
          Based on your website analytics and trending keywords
        </p>
      </CardHeader>
      <CardContent>
        <ul className="space-y-3">
          {topics.map(topic => (
            <li
              key={topic.id}
              className="flex items-center justify-between p-3 bg-muted rounded-md"
            >
              <span className="font-medium">{topic.title}</span>
              <Button
                size="icon"
                variant="ghost"
                className="text-primary hover:text-primary"
              >
                <PlusIcon className="h-5 w-5" />
              </Button>
            </li>
          ))}
        </ul>

        <Button variant="outline" className="mt-4 w-full">
          Generate More Topics
        </Button>
      </CardContent>
    </Card>
  );
}
