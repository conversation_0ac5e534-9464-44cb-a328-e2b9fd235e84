import { Card, CardContent } from '@/components/ui/card';
import {
  BarChart2Icon,
  CoinsIcon,
  FileTextIcon,
  TrendingUpIcon,
} from 'lucide-react';
import { useState } from 'react';

export default function DashboardStats() {
  const [stats] = useState({
    totalArticles: 24,
    articleIncrease: 12,
    websiteTraffic: 1892,
    trafficIncrease: 24,
    creditsRemaining: 300,
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-muted-foreground text-sm">Total Articles</p>
              <h3 className="text-3xl font-bold text-foreground mt-1">
                {stats.totalArticles}
              </h3>
            </div>
            <div className="p-2 bg-accent rounded-md">
              <FileTextIcon className="h-6 w-6 text-accent-foreground" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <span className="text-primary font-medium flex items-center">
              <TrendingUpIcon className="h-4 w-4 mr-1" />
              {stats.articleIncrease}% increase
            </span>
            <span className="text-muted-foreground ml-2">from last month</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-muted-foreground text-sm">Website Traffic</p>
              <h3 className="text-3xl font-bold text-foreground mt-1">
                {stats.websiteTraffic}
              </h3>
            </div>
            <div className="p-2 bg-secondary rounded-md">
              <BarChart2Icon className="h-6 w-6 text-secondary-foreground" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <span className="text-primary font-medium flex items-center">
              <TrendingUpIcon className="h-4 w-4 mr-1" />
              {stats.trafficIncrease}% increase
            </span>
            <span className="text-muted-foreground ml-2">from last month</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-muted-foreground text-sm">Credits Remaining</p>
              <h3 className="text-3xl font-bold text-foreground mt-1">
                {stats.creditsRemaining}
              </h3>
            </div>
            <div className="p-2 bg-muted rounded-md">
              <CoinsIcon className="h-6 w-6 text-muted-foreground" />
            </div>
          </div>
          <div className="mt-4">
            <button className="text-primary font-medium text-sm hover:underline">
              Upgrade Plan
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
