import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const data = [
  { name: 'Jan', pageviews: 400 },
  { name: 'Feb', pageviews: 600 },
  { name: 'Mar', pageviews: 560 },
  { name: 'Apr', pageviews: 800 },
  { name: 'May', pageviews: 750 },
  { name: 'Jun', pageviews: 900 },
];

export default function ContentPerformance() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Content Performance</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar
                dataKey="pageviews"
                fill="hsl(var(--primary))"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
