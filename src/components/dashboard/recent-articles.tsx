import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreVerticalIcon, PlusIcon } from 'lucide-react';
import { useState } from 'react';

type Article = {
  id: number;
  title: string;
  publishedDate: string;
  thumbnail: string;
  status: 'draft' | 'published';
};

export default function RecentArticles() {
  const [articles] = useState<Article[]>([
    {
      id: 1,
      title: '10 SEO Tips for Boosting Your Website Traffic',
      publishedDate: 'April 15, 2025',
      thumbnail: 'https://via.placeholder.com/100x100.png?text=SEO+Tips',
      status: 'published',
    },
    {
      id: 2,
      title: 'How AI is Revolutionizing Content Creation',
      publishedDate: 'April 10, 2025',
      thumbnail: 'https://via.placeholder.com/100x100.png?text=AI+Content',
      status: 'published',
    },
    {
      id: 3,
      title: 'The Ultimate Guide to WordPress Plugins for Bloggers',
      publishedDate: 'April 5, 2025',
      thumbnail: 'https://via.placeholder.com/100x100.png?text=WP+Plugins',
      status: 'draft',
    },
  ]);

  return (
    <Card className="mb-8">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-foreground">Recent Articles</h2>
          <Button>
            <PlusIcon className="h-5 w-5 mr-2" />
            Create New Article
          </Button>
        </div>

        {articles.map(article => (
          <div
            key={article.id}
            className="border-t border-border py-4 flex items-center justify-between"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-md bg-muted flex-shrink-0">
                <svg
                  className="w-full h-full text-muted-foreground"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="8.5" cy="8.5" r="1.5"></circle>
                  <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-base font-medium text-foreground">
                  {article.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {article.status === 'draft'
                    ? `Draft created on ${article.publishedDate}`
                    : `Published on ${article.publishedDate}`}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge
                variant={article.status === 'published' ? 'success' : 'default'}
              >
                {article.status === 'published' ? 'Published' : 'Draft'}
              </Badge>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreVerticalIcon className="h-5 w-5 text-muted-foreground" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Edit</DropdownMenuItem>
                  <DropdownMenuItem>View</DropdownMenuItem>
                  {article.status === 'draft' && (
                    <DropdownMenuItem>Publish</DropdownMenuItem>
                  )}
                  <DropdownMenuItem className="text-destructive">
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        ))}

        <div className="mt-4 text-center">
          <Button
            variant="link"
            className="text-primary font-medium text-sm hover:underline"
          >
            View All Articles
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
