interface AuthHeaderProps {
  title?: string;
  description?: string;
}

export function AuthHeader({
  title = 'Welcome',
  description = 'Create and manage SEO-optimized content for your websites',
}: AuthHeaderProps) {
  return (
    <div className="text-center space-y-2">
      <div className="text-3xl md:text-4xl font-bold text-primary">
        SEO<span className="text-primary">45</span>
      </div>
      <div className="space-y-1">
        <h1 className="text-2xl md:text-3xl font-bold text-foreground">
          {title}
        </h1>
        <p className="text-sm md:text-base text-muted-foreground">
          {description}
        </p>
      </div>
    </div>
  );
}
