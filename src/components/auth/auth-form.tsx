import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Lock, Mail, User } from 'lucide-react';

interface AuthFormProps {
  type: 'signin' | 'signup';
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  loading: boolean;
  error: string | null;
  onForgotPassword?: () => void;
}

export function AuthForm({
  type,
  onSubmit,
  loading,
  error,
  onForgotPassword,
}: AuthFormProps) {
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription className="text-sm">{error}</AlertDescription>
        </Alert>
      )}

      {type === 'signup' && (
        <div className="space-y-2">
          <Label htmlFor={`${type}-name`} className="text-sm font-medium">
            Full Name
          </Label>
          <div className="relative">
            <User className="absolute left-3 top-3.5 h-4 w-4 text-muted-foreground" />
            <Input
              id={`${type}-name`}
              name="fullName"
              type="text"
              placeholder="Enter your full name"
              className="pl-10 h-11"
              required
            />
          </div>
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor={`${type}-email`} className="text-sm font-medium">
          Email
        </Label>
        <div className="relative">
          <Mail className="absolute left-3 top-3.5 h-4 w-4 text-muted-foreground" />
          <Input
            id={`${type}-email`}
            name="email"
            type="email"
            placeholder="Enter your email"
            className="pl-10 h-11"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor={`${type}-password`} className="text-sm font-medium">
          Password
        </Label>
        <div className="relative">
          <Lock className="absolute left-3 top-3.5 h-4 w-4 text-muted-foreground" />
          <Input
            id={`${type}-password`}
            name="password"
            type="password"
            placeholder={
              type === 'signup'
                ? 'Create a password (min. 6 characters)'
                : 'Enter your password'
            }
            className="pl-10 h-11"
            required
            minLength={type === 'signup' ? 6 : undefined}
          />
        </div>
      </div>

      <Button type="submit" className="w-full h-11" disabled={loading}>
        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {type === 'signin' ? 'Sign In' : 'Create Account'}
      </Button>

      {type === 'signin' && onForgotPassword && (
        <Button
          type="button"
          variant="link"
          className="w-full text-sm text-muted-foreground hover:text-primary"
          onClick={onForgotPassword}
        >
          Forgot your password?
        </Button>
      )}

      {type === 'signup' && (
        <p className="text-xs text-muted-foreground text-center">
          By creating an account, you agree to our Terms of Service and Privacy
          Policy
        </p>
      )}
    </form>
  );
}
