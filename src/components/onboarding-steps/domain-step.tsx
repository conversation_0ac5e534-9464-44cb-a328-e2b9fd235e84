import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { domainFormSchema } from '@/lib/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRightIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

type Step1DomainProps = {
  onNext: () => void;
  initialValue?: string;
  onUpdate: (domain: string) => void;
};

type FormValues = z.infer<typeof domainFormSchema>;

export default function Step1Domain({
  onNext,
  initialValue,
  onUpdate,
}: Step1DomainProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(domainFormSchema),
    defaultValues: {
      domain: initialValue || '',
    },
  });

  const onSubmit = (values: FormValues) => {
    onUpdate(values.domain);
    onNext();
  };

  return (
    <Card className="bg-white rounded-lg shadow-sm">
      <CardContent className="p-6 md:p-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-gray-900">
                What's your website?
              </h2>
              <p className="text-gray-600">
                Tell us about your website domain so we can help you get
                started.
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="domain"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base font-medium text-gray-700">
                        Domain Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your domain (e.g., example.com)"
                          {...field}
                          className="text-base"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-blue-800 font-medium">
                      Don't worry about verification
                    </p>
                    <p className="text-sm text-blue-700 mt-1">
                      We'll help you verify your domain ownership in the next
                      steps. Just enter your domain name for now.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                className="bg-primary hover:bg-primary-hover text-white"
              >
                Save & go next
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
