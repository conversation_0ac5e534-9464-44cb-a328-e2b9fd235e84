import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { OnboardingState, OnboardingStep } from '@/types/onboarding';
import {
  BadgeCheck,
  CheckCircle,
  DollarSign,
  Edit,
  Globe,
  Link as LinkIcon,
  Mail,
  Package,
  Tag,
  User,
} from 'lucide-react';
import { StepCard } from '../common';

interface ReviewStepProps {
  onNext: () => void;
  onboardingState: OnboardingState;
  onEditStep?: (step: OnboardingStep) => void;
}

const InfoItem = ({
  icon,
  label,
  value,
}: {
  icon: React.ReactNode;
  label: string;
  value: string | undefined;
}) => (
  <div className="flex items-center gap-3">
    <div className="text-muted-foreground">{icon}</div>
    <div className="flex-1">
      <p className="text-sm text-muted-foreground">{label}</p>
      <p className="font-medium">{value || '-'}</p>
    </div>
  </div>
);

export default function ReviewStep({
  onNext,
  onboardingState,
  onEditStep,
}: ReviewStepProps) {
  const { auth, website, plugin, plan } = onboardingState;

  return (
    <StepCard
      title="Review & Confirm"
      description="Please review your information before completing the onboarding."
    >
      <div className=" grid lg:grid-cols-2 gap-4 grid-cols-1 lg:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="space-y-1">
              <CardTitle>Account Details</CardTitle>
              <CardDescription>Your personal information.</CardDescription>
            </div>
            {onEditStep && (
              <Button
                variant="outline"
                size="icon"
                onClick={() => onEditStep('auth')}
              >
                <Edit className="w-4 h-4" />
              </Button>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            <InfoItem
              icon={<Mail className="w-5 h-5" />}
              label="Email"
              value={auth?.email}
            />
            <Separator />
            <InfoItem
              icon={<User className="w-5 h-5" />}
              label="Full Name"
              value={auth?.fullName}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="space-y-1">
              <CardTitle>Website Information</CardTitle>
              <CardDescription>Your website configuration.</CardDescription>
            </div>
            {onEditStep && (
              <Button
                variant="outline"
                size="icon"
                onClick={() => onEditStep('website')}
              >
                <Edit className="w-4 h-4" />
              </Button>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            <InfoItem
              icon={<Globe className="w-5 h-5" />}
              label="Domain"
              value={website?.domain}
            />
            <Separator />
            <InfoItem
              icon={<LinkIcon className="w-5 h-5" />}
              label="WordPress URL"
              value={website?.wordpressUrl}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="space-y-1">
              <CardTitle>Plugin Status</CardTitle>
              <CardDescription>SEO45 plugin integration.</CardDescription>
            </div>
            {onEditStep && (
              <Button
                variant="outline"
                size="icon"
                onClick={() => onEditStep('plugin')}
              >
                <Edit className="w-4 h-4" />
              </Button>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            <InfoItem
              icon={<Package className="w-5 h-5" />}
              label="Plugin Installed"
              value={plugin?.pluginInstalled ? 'Yes' : 'No'}
            />
            <Separator />
            <InfoItem
              icon={<BadgeCheck className="w-5 h-5" />}
              label="Plugin Verified"
              value={plugin?.isVerified ? 'Yes' : 'No'}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="space-y-1">
              <CardTitle>Selected Plan</CardTitle>
              <CardDescription>Your chosen subscription plan.</CardDescription>
            </div>
            {onEditStep && (
              <Button
                variant="outline"
                size="icon"
                onClick={() => onEditStep('plan')}
              >
                <Edit className="w-4 h-4" />
              </Button>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            <InfoItem
              icon={<Tag className="w-5 h-5" />}
              label="Plan Name"
              value={plan?.planName}
            />
            <Separator />
            <InfoItem
              icon={<DollarSign className="w-5 h-5" />}
              label="Price"
              value={
                plan
                  ? `$${plan.price}/${
                      plan.currency === 'usd' ? 'month' : plan.currency
                    }`
                  : '-'
              }
            />
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end pt-6 border-t">
        <Button size="lg" onClick={onNext}>
          <CheckCircle className="w-5 h-5 mr-2" />
          Confirm & Complete Onboarding
        </Button>
      </div>
    </StepCard>
  );
}
