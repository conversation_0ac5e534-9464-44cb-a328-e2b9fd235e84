import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { mockPlans } from '@/constants/mock-data';
import { PlanStepData } from '@/types/onboarding';
import { CheckCircle } from 'lucide-react';
import { useState } from 'react';
import { StepCard } from '../common';
import { StepNavigation } from '../common/step-navigation';

interface PlanStepProps {
  onNext: () => void;
  onBack?: () => void;
  initialValues?: PlanStepData;
  onUpdate: (data: PlanStepData) => void;
}

type Plan = {
  id: string;
  name: string;
  price: number;
  currency: string;
  features: string[];
};

export default function PlanStep({
  onNext,
  onBack,
  initialValues,
  onUpdate,
}: PlanStepProps) {
  const [selectedPlan, setSelectedPlan] = useState<string>(
    initialValues?.planId || ''
  );

  const handleSelectPlan = (plan: Plan) => {
    setSelectedPlan(plan.id);
    onUpdate({
      planId: plan.id,
      planName: plan.name,
      price: plan.price,
      currency: plan.currency,
    });
  };

  const handleNext = () => {
    if (selectedPlan) {
      onNext();
    }
  };

  return (
    <StepCard
      title="Choose Your Plan"
      description="Select a plan that best fits your needs."
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-8">
        {mockPlans.map((plan: Plan) => (
          <Card
            key={plan.id}
            className={`flex flex-col cursor-pointer transition-all ${
              selectedPlan === plan.id
                ? 'border-primary ring-2 ring-primary'
                : 'hover:shadow-lg'
            }`}
            onClick={() => handleSelectPlan(plan)}
          >
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <CardDescription>
                <span className="text-3xl font-bold">${plan.price}</span>
                <span className="text-muted-foreground">/month</span>
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <ul className="space-y-3">
                {plan.features.map((feature: string) => (
                  <li key={feature} className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    <span className="text-muted-foreground">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                variant={selectedPlan === plan.id ? 'default' : 'outline'}
              >
                {selectedPlan === plan.id ? 'Selected' : 'Select Plan'}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
      <StepNavigation
        onNext={handleNext}
        onBack={onBack}
        isNextDisabled={!selectedPlan}
        nextLabel="Continue"
      />
    </StepCard>
  );
}
