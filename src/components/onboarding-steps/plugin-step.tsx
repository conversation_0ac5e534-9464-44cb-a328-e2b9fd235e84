import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { OnboardingStepProps, PluginStepData } from '@/types/onboarding';
import { useMutation } from '@tanstack/react-query';
import { Check, CheckCircle, Download, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { StepCard } from '../common';
import { StepNavigation } from '../common/step-navigation';

interface PluginStepProps extends OnboardingStepProps<PluginStepData> {
  initialValues?: PluginStepData;
  onBack?: () => void;
}

export default function PluginStep({
  onNext,
  onBack,
  initialValues,
  onUpdate,
}: PluginStepProps) {
  const [pluginState, setPluginState] = useState<PluginStepData>(
    initialValues || { pluginInstalled: false, isVerified: false }
  );

  const verifyPluginMutation = useMutation({
    mutationFn: async () => {
      await new Promise(resolve => setTimeout(resolve, 1500));
      return { success: true, verified: true };
    },
    onSuccess: () => {
      const newState = { ...pluginState, isVerified: true };
      setPluginState(newState);
      onUpdate(newState);
      toast.success('Plugin verified successfully!');
    },
    onError: (error: Error) => {
      toast.error('Verification failed', {
        description:
          error.message ||
          'Could not verify plugin installation. Please try again.',
      });
    },
  });

  const handleDownloadPlugin = () => {
    const newState = { ...pluginState, pluginInstalled: true };
    setPluginState(newState);
    onUpdate(newState);
    toast.info('Plugin download started...');
  };

  const handleVerifyConnection = () => {
    verifyPluginMutation.mutate();
  };

  const handleNext = () => {
    if (pluginState.isVerified) {
      onNext();
    }
  };

  return (
    <StepCard
      title="Install SEO45 Plugin"
      description="Integrate your WordPress site by installing our plugin."
    >
      <div className="space-y-8 py-8">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Step 1: Download & Install</CardTitle>
                <CardDescription className="mt-1">
                  Download the plugin and upload it to your WordPress site.
                </CardDescription>
              </div>
              {pluginState.pluginInstalled && (
                <CheckCircle className="w-8 h-8 text-green-500" />
              )}
            </div>
          </CardHeader>
          <CardContent>
            <Button
              onClick={handleDownloadPlugin}
              disabled={pluginState.pluginInstalled}
            >
              <Download className="mr-2 h-4 w-4" />
              Download Plugin
            </Button>
            {pluginState.pluginInstalled && (
              <p className="text-sm text-green-600 mt-3">
                Plugin downloaded. Please proceed to install and activate it on
                your WordPress admin dashboard.
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Step 2: Verify Connection</CardTitle>
                <CardDescription className="mt-1">
                  After activating the plugin, verify the connection.
                </CardDescription>
              </div>
              {pluginState.isVerified && (
                <CheckCircle className="w-8 h-8 text-green-500" />
              )}
            </div>
          </CardHeader>
          <CardContent>
            <Button
              onClick={handleVerifyConnection}
              disabled={
                !pluginState.pluginInstalled ||
                pluginState.isVerified ||
                verifyPluginMutation.isPending
              }
            >
              {verifyPluginMutation.isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Check className="mr-2 h-4 w-4" />
              )}
              Verify Connection
            </Button>
            {pluginState.isVerified && (
              <p className="text-sm text-green-600 mt-3">
                Connection verified successfully! You can now proceed.
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      <StepNavigation
        onNext={handleNext}
        onBack={onBack}
        isNextDisabled={!pluginState.isVerified}
      />
    </StepCard>
  );
}
