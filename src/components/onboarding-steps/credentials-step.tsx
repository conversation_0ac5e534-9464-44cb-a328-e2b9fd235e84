import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { CredentialsStepData, OnboardingStepProps } from '@/types/onboarding';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { StepCard } from '../common';
import { StepNavigation } from '../common/step-navigation';

const wordpressCredentialsSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

type FormValues = z.infer<typeof wordpressCredentialsSchema>;

interface CredentialStepProps extends OnboardingStepProps<CredentialsStepData> {
  initialValues?: CredentialsStepData;
  onBack?: () => void;
}

export default function CredentialStep({
  onNext,
  onBack,
  initialValues,
  onUpdate,
}: CredentialStepProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(wordpressCredentialsSchema),
    defaultValues: initialValues || { username: '', password: '' },
  });

  const onSubmit = (values: FormValues) => {
    onUpdate(values);
    onNext();
  };

  return (
    <StepCard
      title="WordPress Credentials"
      description="Enter your WordPress admin credentials to connect your site."
    >
      <Alert variant="default">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Your credentials are encrypted and stored securely. We only use them
          to manage your content.
        </AlertDescription>
      </Alert>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pt-6">
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>WordPress Username</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your WordPress username"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>WordPress Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Enter your WordPress password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <StepNavigation
            onNext={form.handleSubmit(onSubmit)}
            onBack={onBack}
          />
        </form>
      </Form>
    </StepCard>
  );
}
