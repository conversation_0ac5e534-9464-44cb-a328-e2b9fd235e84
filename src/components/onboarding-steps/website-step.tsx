import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { OnboardingStepProps, WebsiteStepData } from '@/types/onboarding';
import { StepCard } from '../common';
import { StepNavigation } from '../common/step-navigation';

const formSchema = z.object({
  domain: z.string().min(1, 'Domain is required'),
  wordpressUrl: z.string().url('Invalid URL').optional().or(z.literal('')),
});

interface WebsiteStepProps extends OnboardingStepProps<WebsiteStepData> {
  initialValues?: WebsiteStepData;
  onBack?: () => void;
}

export default function WebsiteStep({
  onNext,
  onBack,
  onUpdate,
  initialValues,
}: WebsiteStepProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: initialValues || { domain: '', wordpressUrl: '' },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    onUpdate(values);
    onNext();
  }

  return (
    <StepCard
      title="Website Information"
      description="Please provide your website details so we can connect to it."
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="domain"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Domain</FormLabel>
                <FormControl>
                  <Input placeholder="example.com" {...field} />
                </FormControl>
                <FormDescription>Your website domain.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="wordpressUrl"
            render={({ field }) => (
              <FormItem>
                <FormLabel>WordPress URL (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="https://example.com" {...field} />
                </FormControl>
                <FormDescription>
                  The URL of your WordPress installation.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <StepNavigation
            onNext={form.handleSubmit(onSubmit)}
            onBack={onBack}
          />
        </form>
      </Form>
    </StepCard>
  );
}
