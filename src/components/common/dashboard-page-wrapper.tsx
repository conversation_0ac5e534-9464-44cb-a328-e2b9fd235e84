import { cn } from '@/lib/utils';
import * as React from 'react';

export interface DashboardPageWrapper {
  className?: string;
  children: React.ReactNode;
}

export const DashboardPageWrapper = ({
  className,
  children,
}: DashboardPageWrapper) => {
  return (
    <div
      className={cn(
        'w-full min-h-screen bg-background ',

        className
      )}
    >
      {children}
    </div>
  );
};
