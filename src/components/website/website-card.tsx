import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Subscription, Website } from '@/supabase/types';
import { format, formatDistanceToNow } from 'date-fns';
import {
  Calendar,
  Clock,
  ExternalLink,
  Eye,
  FileText,
  Globe,
  Server,
  Settings,
  Shield,
  Tag,
} from 'lucide-react';

import { motion } from 'framer-motion';
import { Button } from '../ui/button';

interface WebsiteCardProps {
  website: Website;
  subscriptions?: Subscription[];
  showArticleCount?: boolean;
  className?: string;
}

export const WebsiteCard: React.FC<WebsiteCardProps> = ({
  website,
  subscriptions = [],
  showArticleCount = true,
  className = '',
}) => {
  const activeSubscription = subscriptions.find(sub => sub.status === 'active');

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'active':
        return 'bg-green-800/20 text-green-700 border-green-500/30 ';
      case 'inactive':
        return 'bg-red-500/20 text-red-700 border-red-500/30';
      case 'maintenance':
        return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30';
      case 'setup-pending':
        return 'bg-blue-500/20 text-blue-700 border-blue-500/30';
      default:
        return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
    }
  };

  // Generate random article count for demo (10-50)
  const getArticleCount = (_websiteId: number) => {
    return Math.floor(Math.random() * 40) + 10;
  };

  const articleCount = getArticleCount(website.id);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        className={`group  transition-all duration-300  border-border/50 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm ${className}`}
      >
        <CardContent className="p-6">
          {/* Header Section */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-4 mb-3">
                <motion.div
                  className="relative p-3 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border border-primary/20"
                  transition={{ duration: 0.2 }}
                >
                  <Globe className="h-6 w-6 text-primary" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-card"></div>
                </motion.div>
                <div className="min-w-0 flex-1">
                  <h3 className="font-bold text-xl text-foreground truncate mb-1">
                    {website.domain_name}
                  </h3>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <ExternalLink className="h-4 w-4" />
                    <span className="truncate hover:text-primary transition-colors cursor-pointer">
                      {website.website_url}
                    </span>
                  </div>
                </div>
              </div>
              {website.website_description && (
                <p className="text-sm text-muted-foreground line-clamp-2 mt-3 leading-relaxed">
                  {website.website_description}
                </p>
              )}
            </div>
            <motion.div transition={{ duration: 0.2 }}>
              <Badge
                variant={'outline'}
                className={`${getStatusColor(website.status)} font-medium px-3 py-1`}
              >
                {website.status || 'setup-pending'}
              </Badge>
            </motion.div>
          </div>

          {/* Main Info Grid */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            {/* Article Count */}
            {showArticleCount && (
              <motion.div className="flex items-center gap-3 p-4 bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 rounded-xl border border-green-200/50 dark:border-green-800/30 hover:shadow-md transition-all duration-200">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <FileText className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <div className="text-xl font-bold text-green-700 dark:text-green-300">
                    {articleCount}
                  </div>
                  <div className="text-xs font-medium text-green-600 dark:text-green-400">
                    Articles
                  </div>
                </div>
              </motion.div>
            )}

            {/* SSL Status */}
            <motion.div className="flex items-center gap-3 p-4 bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/20 dark:to-purple-800/10 rounded-xl border border-purple-200/50 dark:border-purple-800/30 hover:shadow-md transition-all duration-200">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Shield className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-xl font-bold text-purple-700 dark:text-purple-300">
                  {website.ssl_enabled ? 'Yes' : 'No'}
                </div>
                <div className="text-xs font-medium text-purple-600 dark:text-purple-400">
                  SSL Enabled
                </div>
              </div>
            </motion.div>

            {/* Hosting Provider */}
            {website.hosting_provider && (
              <motion.div className="flex items-center gap-3 p-4 bg-gradient-to-br from-orange-50 to-orange-100/50 dark:from-orange-900/20 dark:to-orange-800/10 rounded-xl border border-orange-200/50 dark:border-orange-800/30 col-span-2 hover:shadow-md transition-all duration-200">
                <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                  <Server className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <div className="text-lg font-bold text-orange-700 dark:text-orange-300">
                    {website.hosting_provider}
                  </div>
                  <div className="text-xs font-medium text-orange-600 dark:text-orange-400">
                    Hosting Provider
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Additional Info */}
          <div className="space-y-4">
            {/* Website Niche */}
            {website.website_niche && (
              <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Tag className="h-4 w-4" />
                  <span>Niche</span>
                </div>
                <Badge
                  variant="outline"
                  className="bg-primary/10 text-primary border-primary/20"
                >
                  {website.website_niche}
                </Badge>
              </div>
            )}

            {/* Subscription Info */}
            <div className="p-4 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/50">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-foreground">
                  Subscription Status
                </span>
                <Badge
                  variant={activeSubscription ? 'default' : 'secondary'}
                  className={
                    activeSubscription
                      ? 'bg-gradient-to-tr from-green-300/10 to-green-200/20 dark:from-green-900/20 dark:to-green-800/10 border-green-200'
                      : ''
                  }
                >
                  {activeSubscription ? 'Active' : 'None'}
                </Badge>
              </div>
              {activeSubscription && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>
                    Expires{' '}
                    {format(
                      new Date(activeSubscription.current_period_end),
                      'MMM dd, yyyy'
                    )}
                  </span>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 hover:bg-primary/10 hover:border-primary/30 transition-all duration-200"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex-1 hover:bg-primary/10 hover:border-primary/30 transition-all duration-200"
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage
              </Button>
            </div>

            {/* Created Date */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground pt-2 border-t border-border/30">
              <Clock className="h-3 w-3" />
              <span>
                Added{' '}
                {formatDistanceToNow(new Date(website.created_at || ''), {
                  addSuffix: true,
                })}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
