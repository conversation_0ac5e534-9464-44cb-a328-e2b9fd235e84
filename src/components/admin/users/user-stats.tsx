import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { UserWithAuthData } from '@/types/admin/user-management';
import { Crown, Shield, ShieldAlert, UserCheck, Users } from 'lucide-react';
import React from 'react';

interface UserStatsProps {
  users: UserWithAuthData[];
  total: number;
}

export const UserStats: React.FC<UserStatsProps> = ({ users, total }) => {
  const activeUsers = users.filter(
    user => user.auth_user?.email_confirmed_at
  ).length;
  const adminUsers = users.filter(user => user.role === 'admin').length;
  const moderatorUsers = users.filter(user => user.role === 'moderator').length;
  const usersWithSubscriptions = users.filter(
    user => user.subscription_count && user.subscription_count > 0
  ).length;

  const stats = [
    {
      title: 'Total Users',
      value: total,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Active Users',
      value: activeUsers,
      icon: User<PERSON>heck,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Admins',
      value: adminUsers,
      icon: Shield,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Moderators',
      value: moderatorUsers,
      icon: ShieldAlert,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'With Subscriptions',
      value: usersWithSubscriptions,
      icon: Crown,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
      {stats.map(stat => {
        const Icon = stat.icon;
        return (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              {stat.title !== 'Total Users' && (
                <p className="text-xs text-muted-foreground">
                  {total > 0
                    ? `${((stat.value / total) * 100).toFixed(1)}%`
                    : '0%'}{' '}
                  of total
                </p>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default UserStats;
