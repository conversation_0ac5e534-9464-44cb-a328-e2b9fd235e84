import { FormSelectField, FormTextField } from '@/components/forms';
import { But<PERSON> } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  CreateUserData,
  UpdateUserData,
  UserWithAuthData,
} from '@/types/admin/user-management';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const profileFormSchema = z.object({
  full_name: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  username: z.string().optional().or(z.literal('')),
  role: z.string().optional().or(z.literal('')),
  website: z.string().url('Invalid URL').optional().or(z.literal('')),
  avatar_url: z.string().url('Invalid URL').optional().or(z.literal('')),
});

const authFormSchema = z.object({
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .optional()
    .or(z.literal('')),
  send_email_confirmation: z.boolean().default(true),
});

type ProfileFormData = z.infer<typeof profileFormSchema>;
type AuthFormData = z.infer<typeof authFormSchema>;

interface UserFormProps {
  user?: UserWithAuthData;
  onSave: (data: CreateUserData | UpdateUserData) => Promise<void>;
  onCancel: () => void;
  isCreating?: boolean;
}

export const UserForm: React.FC<UserFormProps> = ({
  user,
  onSave,
  onCancel,
  isCreating = !user,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      full_name: '',
      email: '',
      username: '',
      role: 'user',
      website: '',
      avatar_url: '',
    },
  });

  const authForm = useForm<AuthFormData>({
    resolver: zodResolver(authFormSchema),
    defaultValues: {
      password: '',
      send_email_confirmation: true,
    },
  });

  // Update form when user data changes
  useEffect(() => {
    if (user) {
      profileForm.reset({
        full_name: user.full_name || '',
        email: user.email || '',
        username: user.username || '',
        role: user.role || 'user',
        website: user.website || '',
        avatar_url: user.avatar_url || '',
      });
    } else {
      profileForm.reset({
        full_name: '',
        email: '',
        username: '',
        role: 'user',
        website: '',
        avatar_url: '',
      });
    }

    authForm.reset({
      password: '',
      send_email_confirmation: true,
    });
  }, [user, profileForm, authForm]);

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      const profileValid = await profileForm.trigger();
      if (!profileValid) {
        setActiveTab('profile');
        return;
      }

      const profileData = profileForm.getValues();

      if (isCreating) {
        const authValid = await authForm.trigger();
        if (!authValid) {
          setActiveTab('auth');
          return;
        }
        const authData = authForm.getValues();

        const createData: CreateUserData = {
          full_name: profileData.full_name,
          email: profileData.email,
          username: profileData.username || undefined,
          role: profileData.role || undefined,
          website: profileData.website || undefined,
          avatar_url: profileData.avatar_url || undefined,
          password: authData.password || undefined,
          send_email_confirmation: authData.send_email_confirmation,
        };
        await onSave(createData);
      } else {
        const updateData: UpdateUserData = {
          full_name: profileData.full_name,
          email: profileData.email,
          username: profileData.username || undefined,
          role: profileData.role || undefined,
          website: profileData.website || undefined,
          avatar_url: profileData.avatar_url || undefined,
        };

        // If password is provided, add it to the update
        const authData = authForm.getValues();
        if (authData.password) {
          const updateWithPassword = {
            ...updateData,
            password: authData.password,
          };
          await onSave(updateWithPassword);
        } else {
          await onSave(updateData);
        }
      }
    } catch (error) {
      console.error('Error saving user:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="profile">Profile Information</TabsTrigger>
          <TabsTrigger value="auth">Authentication</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Form {...profileForm}>
            <div className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Basic Information</h3>

                <FormTextField
                  name="full_name"
                  label="Full Name"
                  placeholder="Enter full name"
                  required
                />

                <FormTextField
                  name="email"
                  label="Email Address"
                  placeholder="Enter email address"
                  type="email"
                  required
                />

                <FormTextField
                  name="username"
                  label="Username"
                  placeholder="Enter username (optional)"
                  description="Used for mentions and unique identification"
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Role and Permissions</h3>

                <FormSelectField
                  name="role"
                  label="User Role"
                  placeholder="Select a role"
                  options={[
                    { value: 'user', label: 'User - Standard access' },
                    {
                      value: 'moderator',
                      label: 'Moderator - Content management',
                    },
                    { value: 'admin', label: 'Admin - Full system access' },
                  ]}
                  description="Determines the user's access level and permissions"
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Profile Details</h3>

                <FormTextField
                  name="website"
                  label="Website URL"
                  placeholder="https://example.com"
                  type="url"
                  description="User's personal or business website"
                />

                <FormTextField
                  name="avatar_url"
                  label="Avatar URL"
                  placeholder="https://example.com/avatar.jpg"
                  type="url"
                  description="URL to the user's profile picture"
                />
              </div>
            </div>
          </Form>
        </TabsContent>

        <TabsContent value="auth" className="space-y-6">
          <Form {...authForm}>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Authentication Settings</h3>

              <FormTextField
                name="password"
                label={isCreating ? 'Password' : 'New Password'}
                placeholder="Enter password"
                type="password"
                required={isCreating}
                description={
                  isCreating
                    ? 'Minimum 8 characters. User will be prompted to change on first login.'
                    : 'Leave empty to keep current password'
                }
              />

              {isCreating && (
                <div className="flex items-center space-x-2">
                  <Switch
                    id="send_email_confirmation"
                    checked={authForm.watch('send_email_confirmation')}
                    onCheckedChange={checked =>
                      authForm.setValue('send_email_confirmation', checked)
                    }
                  />
                  <Label htmlFor="send_email_confirmation">
                    Send email confirmation
                  </Label>
                </div>
              )}
            </div>
          </Form>
        </TabsContent>
      </Tabs>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting
            ? isCreating
              ? 'Creating...'
              : 'Updating...'
            : isCreating
              ? 'Create User'
              : 'Update User'}
        </Button>
      </div>
    </div>
  );
};

export default UserForm;
