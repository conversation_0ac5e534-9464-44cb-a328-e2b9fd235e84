import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { AdminUserService } from '@/services/admin/user-management-service';
import { getAllPlans } from '@/services/plan/plan-service';
import {
  createSubscription,
  updateSubscription,
} from '@/services/subscription/subscription-service';
import { UserWithAuthData } from '@/types/admin/user-management';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Calendar, Check, Crown, Loader2, Plus, X } from 'lucide-react';
import React, { useState } from 'react';

interface UserSubscriptionManagerProps {
  user: UserWithAuthData | null;
  isOpen: boolean;
  onClose: () => void;
}

export const UserSubscriptionManager: React.FC<
  UserSubscriptionManagerProps
> = ({ user, isOpen, onClose }) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedPlanId, setSelectedPlanId] = useState<string>('');

  // Fetch available plans
  const { data: plans = [], isLoading: plansLoading } = useQuery({
    queryKey: ['plans'],
    queryFn: getAllPlans,
    enabled: isOpen,
  });

  // Fetch user's subscription details
  const {
    data: subscriptions = [],
    isLoading: subscriptionsLoading,
    refetch: refetchSubscriptions,
  } = useQuery({
    queryKey: ['user-subscriptions', user?.id],
    queryFn: () => {
      if (!user?.id) throw new Error('User ID required');
      return AdminUserService.getUserSubscriptionSummary(user.id);
    },
    enabled: isOpen && !!user?.id,
  });

  // Create subscription mutation
  const createSubscriptionMutation = useMutation({
    mutationFn: async (planId: number) => {
      if (!user?.id) throw new Error('User ID required');

      const now = new Date();
      const periodEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days

      return createSubscription({
        user_id: user.id,
        plan_id: planId,
        status: 'active',
        current_period_start: now.toISOString(),
        current_period_end: periodEnd.toISOString(),
      });
    },
    onSuccess: () => {
      toast({
        title: 'Subscription Created',
        description: 'User subscription has been created successfully.',
      });
      refetchSubscriptions();
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setSelectedPlanId('');
    },
    onError: error => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Cancel subscription mutation
  const cancelSubscriptionMutation = useMutation({
    mutationFn: async (subscriptionId: number) => {
      return updateSubscription(subscriptionId, {
        cancel_at_period_end: true,
        updated_at: new Date().toISOString(),
      });
    },
    onSuccess: () => {
      toast({
        title: 'Subscription Canceled',
        description: 'Subscription will end at the current period.',
      });
      refetchSubscriptions();
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: error => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Reactivate subscription mutation
  const reactivateSubscriptionMutation = useMutation({
    mutationFn: async (subscriptionId: number) => {
      return updateSubscription(subscriptionId, {
        cancel_at_period_end: false,
        updated_at: new Date().toISOString(),
      });
    },
    onSuccess: () => {
      toast({
        title: 'Subscription Reactivated',
        description: 'Subscription has been reactivated successfully.',
      });
      refetchSubscriptions();
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: error => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleCreateSubscription = () => {
    if (!selectedPlanId) return;
    createSubscriptionMutation.mutate(parseInt(selectedPlanId));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price / 100);
  };

  const getStatusBadgeVariant = (
    status: string,
    cancelAtPeriodEnd?: boolean
  ) => {
    if (status === 'active' && !cancelAtPeriodEnd) return 'default';
    if (status === 'active' && cancelAtPeriodEnd) return 'secondary';
    if (status === 'trialing') return 'secondary';
    return 'outline';
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Manage Subscriptions - {user.full_name || user.email}
          </DialogTitle>
          <DialogDescription>
            View and manage this user's subscription status and billing
            information.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Create New Subscription */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4">
              Create New Subscription
            </h3>
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <Select
                  value={selectedPlanId}
                  onValueChange={setSelectedPlanId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a plan" />
                  </SelectTrigger>
                  <SelectContent>
                    {plans.map(plan => (
                      <SelectItem key={plan.id} value={plan.id.toString()}>
                        {plan.name} - {formatPrice(plan.price)}/{plan.interval}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button
                onClick={handleCreateSubscription}
                disabled={
                  !selectedPlanId ||
                  createSubscriptionMutation.isPending ||
                  plansLoading
                }
              >
                {createSubscriptionMutation.isPending ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Plus className="w-4 h-4 mr-2" />
                )}
                Create Subscription
              </Button>
            </div>
          </div>

          {/* Current Subscriptions */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4">Current Subscriptions</h3>

            {subscriptionsLoading ? (
              <div className="text-center p-8">
                <Loader2 className="w-8 h-8 animate-spin mx-auto" />
                <p className="mt-2 text-sm text-gray-500">
                  Loading subscriptions...
                </p>
              </div>
            ) : subscriptions.length === 0 ? (
              <div className="text-center p-8">
                <Crown className="w-12 h-12 text-gray-400 mx-auto" />
                <h4 className="mt-4 text-lg font-medium text-gray-900">
                  No Subscriptions
                </h4>
                <p className="mt-2 text-sm text-gray-500">
                  This user doesn't have any subscriptions yet.
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Current Period</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {subscriptions.map((subscription: unknown) => {
                    const sub = subscription as Record<string, unknown>;
                    return (
                      <TableRow key={sub.id as string}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">
                              {((sub.plans as Record<string, unknown>)
                                ?.name as string) || 'Unknown Plan'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {(sub.plans as Record<string, unknown>)?.price
                                ? formatPrice(
                                    (sub.plans as Record<string, unknown>)
                                      .price as number
                                  )
                                : 'N/A'}
                              /
                              {((sub.plans as Record<string, unknown>)
                                ?.interval as string) || 'month'}
                            </div>
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="space-y-1">
                            <Badge
                              variant={getStatusBadgeVariant(
                                sub.status as string,
                                sub.cancel_at_period_end as boolean
                              )}
                            >
                              {sub.status === 'active' &&
                              sub.cancel_at_period_end
                                ? 'Ending Soon'
                                : (sub.status as string)
                                    ?.charAt(0)
                                    .toUpperCase() +
                                    (sub.status as string)?.slice(1) ||
                                  'Unknown'}
                            </Badge>
                            {(sub.cancel_at_period_end as boolean) && (
                              <div className="text-xs text-gray-500">
                                Ends{' '}
                                {formatDate(sub.current_period_end as string)}
                              </div>
                            )}
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <Calendar className="w-4 h-4 mr-1" />
                              {formatDate(sub.current_period_start as string)}
                            </div>
                            <div className="text-xs text-gray-500">
                              to {formatDate(sub.current_period_end as string)}
                            </div>
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="text-sm text-gray-500">
                            {(sub.created_at as string) &&
                              formatDate(sub.created_at as string)}
                          </div>
                        </TableCell>

                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            {sub.status === 'active' &&
                            (sub.cancel_at_period_end as boolean) ? (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  reactivateSubscriptionMutation.mutate(
                                    sub.id as number
                                  )
                                }
                                disabled={
                                  reactivateSubscriptionMutation.isPending
                                }
                              >
                                {reactivateSubscriptionMutation.isPending ? (
                                  <Loader2 className="w-4 h-4 animate-spin" />
                                ) : (
                                  <Check className="w-4 h-4" />
                                )}
                              </Button>
                            ) : sub.status === 'active' ? (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  cancelSubscriptionMutation.mutate(
                                    sub.id as number
                                  )
                                }
                                disabled={cancelSubscriptionMutation.isPending}
                              >
                                {cancelSubscriptionMutation.isPending ? (
                                  <Loader2 className="w-4 h-4 animate-spin" />
                                ) : (
                                  <X className="w-4 h-4" />
                                )}
                              </Button>
                            ) : null}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserSubscriptionManager;
