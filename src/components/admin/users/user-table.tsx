import { DataTable } from '@/components/data-table/data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UserWithAuthData } from '@/types/admin/user-management';
import { ColumnDef } from '@tanstack/react-table';
import {
  Crown,
  Edit,
  Key,
  MoreHorizontal,
  Shield,
  ShieldAlert,
  Trash2,
  User,
  UserCheck,
  UserX,
} from 'lucide-react';
import React from 'react';

interface UserTableProps {
  users: UserWithAuthData[];
  onEdit: (user: UserWithAuthData) => void;
  onDelete: (user: UserWithAuthData) => void;
  onManageSubscriptions: (user: UserWithAuthData) => void;
  onResetPassword: (user: UserWithAuthData) => void;
  onToggleStatus: (user: UserWithAuthData) => void;
  loading?: boolean;
}

const getRoleIcon = (role: string | null) => {
  switch (role) {
    case 'admin':
      return <Crown className="h-4 w-4 text-yellow-500" />;
    case 'moderator':
      return <Shield className="h-4 w-4 text-blue-500" />;
    default:
      return <User className="h-4 w-4 text-gray-500" />;
  }
};

const getRoleBadgeVariant = (
  role: string | null
): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (role) {
    case 'admin':
      return 'destructive';
    case 'moderator':
      return 'secondary';
    default:
      return 'outline';
  }
};

const formatDate = (dateString: string | null) => {
  if (!dateString) return 'Never';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const UserTable: React.FC<UserTableProps> = ({
  users,
  onEdit,
  onDelete,
  onManageSubscriptions,
  onResetPassword,
  onToggleStatus,
}) => {
  const columns: ColumnDef<UserWithAuthData>[] = [
    {
      accessorKey: 'email',
      header: 'User',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              {user.avatar_url ? (
                <img
                  className="h-8 w-8 rounded-full"
                  src={user.avatar_url}
                  alt={user.full_name || user.email || ''}
                />
              ) : (
                <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                  <User className="h-4 w-4 text-gray-500" />
                </div>
              )}
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user.full_name || 'Unnamed User'}
              </p>
              <p className="text-sm text-gray-500 truncate">{user.email}</p>
              {user.username && (
                <p className="text-xs text-gray-400">@{user.username}</p>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => {
        const role = row.getValue('role') as string | null;
        return (
          <div className="flex items-center space-x-2">
            {getRoleIcon(role)}
            <Badge variant={getRoleBadgeVariant(role)}>{role || 'user'}</Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'auth_status',
      header: 'Status',
      cell: ({ row }) => {
        const user = row.original;
        const isConfirmed = user.auth_user?.email_confirmed_at;
        return (
          <div className="flex items-center space-x-2">
            {isConfirmed ? (
              <UserCheck className="h-4 w-4 text-green-500" />
            ) : (
              <UserX className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm">
              {isConfirmed ? 'Verified' : 'Unverified'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'subscriptions',
      header: 'Subscription',
      cell: ({ row }) => {
        const user = row.original;
        if (!user.subscriptions || user.subscriptions.length === 0) {
          return <Badge variant="outline">No subscription</Badge>;
        }

        const activeSubscription =
          user.subscriptions.find(s => s.status === 'active') ||
          user.subscriptions[0];
        const statusVariant =
          activeSubscription.status === 'active' ? 'default' : 'secondary';

        return (
          <div className="space-y-1">
            <Badge variant={statusVariant}>{activeSubscription.status}</Badge>
            {activeSubscription.plans?.name && (
              <p className="text-xs text-gray-500">
                {activeSubscription.plans.name}
              </p>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Joined',
      cell: ({ row }) => {
        const date = row.getValue('created_at') as string;
        return (
          <span className="text-sm text-gray-500">{formatDate(date)}</span>
        );
      },
    },
    {
      accessorKey: 'last_login',
      header: 'Last Login',
      cell: ({ row }) => {
        const user = row.original;
        const lastLogin =
          user.auth_user?.last_sign_in_at || user.last_login || null;
        return (
          <span className="text-sm text-gray-500">{formatDate(lastLogin)}</span>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const user = row.original;
        const isConfirmed = user.auth_user?.email_confirmed_at;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>User Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={() => onEdit(user)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Profile
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => onManageSubscriptions(user)}>
                <ShieldAlert className="mr-2 h-4 w-4" />
                Manage Subscription
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => onResetPassword(user)}>
                <Key className="mr-2 h-4 w-4" />
                Reset Password
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => onToggleStatus(user)}>
                {isConfirmed ? (
                  <>
                    <UserX className="mr-2 h-4 w-4" />
                    Disable Account
                  </>
                ) : (
                  <>
                    <UserCheck className="mr-2 h-4 w-4" />
                    Enable Account
                  </>
                )}
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem
                onClick={() => onDelete(user)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete User
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return <DataTable columns={columns} data={users} searchKey="email" />;
};

export default UserTable;
