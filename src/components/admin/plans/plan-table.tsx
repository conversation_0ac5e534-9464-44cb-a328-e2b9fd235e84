import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Plan } from '@/supabase/types';
import {
  Crown,
  DollarSign,
  Edit,
  Eye,
  Globe,
  Hash,
  MoreHorizontal,
  Trash2,
} from 'lucide-react';
import React from 'react';

interface PlanTableProps {
  plans: Plan[];
  onEdit: (plan: Plan) => void;
  onDelete: (id: number) => void;
  onView?: (plan: Plan) => void;
}

const PlanTable: React.FC<PlanTableProps> = ({
  plans,
  onEdit,
  onDelete,
  onView,
}) => {
  const formatPrice = (price: number, interval: string) => {
    const periodText = interval === 'year' ? 'year' : 'month';
    return `$${price.toFixed(2)}/${periodText}`;
  };

  const getStatusBadge = (isActive: boolean | null) => {
    return (
      <Badge variant={isActive ? 'default' : 'secondary'}>
        {isActive ? 'Active' : 'Inactive'}
      </Badge>
    );
  };

  const getPopularBadge = () => {
    return (
      <Badge variant="destructive" className="flex items-center gap-1">
        <Crown className="h-3 w-3" />
        Popular
      </Badge>
    );
  };

  const formatFeaturesList = (features: unknown) => {
    if (!features || typeof features !== 'object') return [];
    const featuresObj = features as Record<string, boolean>;
    return Object.keys(featuresObj).filter(key => featuresObj[key] === true);
  };

  if (plans.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <DollarSign className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-center mb-2">
            No Plans Found
          </h3>
          <p className="text-muted-foreground text-center max-w-md">
            Get started by creating your first subscription plan.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Subscription Plans</CardTitle>
            <CardDescription>
              Manage your subscription plans and pricing
            </CardDescription>
          </div>
          <Badge variant="outline" className="text-sm">
            {plans.length} {plans.length === 1 ? 'plan' : 'plans'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Plan Details</TableHead>
                <TableHead>Pricing</TableHead>
                <TableHead>Features</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {plans.map(plan => {
                const features = formatFeaturesList(plan.features);
                return (
                  <TableRow key={plan.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{plan.name}</span>
                          {/* Show popular badge for plans marked as popular in the future */}
                          {plan.name.toLowerCase().includes('popular') &&
                            getPopularBadge()}
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {plan.description || 'No description'}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Globe className="h-3 w-3" />
                            {/* Future: show max_websites when available */}
                            Unlimited sites
                          </span>
                          <span className="flex items-center gap-1">
                            <Hash className="h-3 w-3" />
                            {/* Future: show max_keywords when available */}
                            Unlimited keywords
                          </span>
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">
                          {formatPrice(plan.price, plan.interval)}
                        </div>
                        {plan.discounted_price && (
                          <div className="text-sm text-green-600">
                            Sale:{' '}
                            {formatPrice(plan.discounted_price, plan.interval)}
                          </div>
                        )}
                        <Badge variant="outline" className="text-xs capitalize">
                          {plan.interval}ly
                        </Badge>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-2">
                        {features.length > 0 ? (
                          <>
                            <div className="flex flex-wrap gap-1">
                              {features.slice(0, 3).map(feature => (
                                <Badge
                                  key={feature}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {feature}
                                </Badge>
                              ))}
                            </div>
                            {features.length > 3 && (
                              <p className="text-xs text-muted-foreground">
                                +{features.length - 3} more features
                              </p>
                            )}
                          </>
                        ) : (
                          <span className="text-sm text-muted-foreground">
                            No features defined
                          </span>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>{getStatusBadge(plan.is_active)}</TableCell>

                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          {onView && (
                            <>
                              <DropdownMenuItem onClick={() => onView(plan)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                            </>
                          )}
                          <DropdownMenuItem onClick={() => onEdit(plan)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Plan
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onDelete(plan.id)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Plan
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default PlanTable;
