import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { getAllPlans } from '@/services/plan/plan-service';
import { seedAnnualPlans, seedPlans } from '@/utils/seed-plans';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

export default function PlanSeeder() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSeeding, setIsSeeding] = useState(false);

  const { data: plans = [], refetch } = useQuery({
    queryKey: ['plans'],
    queryFn: getAllPlans,
  });

  const handleSeedPlans = async () => {
    setIsSeeding(true);
    try {
      await seedPlans();
      await seedAnnualPlans();

      toast({
        title: 'Plans Seeded Successfully',
        description: 'All subscription plans have been created.',
      });

      await refetch();
      queryClient.invalidateQueries({ queryKey: ['plans'] });
    } catch (error) {
      toast({
        title: 'Error Seeding Plans',
        description:
          error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Plan Management</CardTitle>
        <p className="text-muted-foreground">
          Manage subscription plans in the database
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">Current Plans Count</p>
            <p className="text-sm text-muted-foreground">
              {plans.length} plans found in database
            </p>
          </div>
          <Button
            onClick={handleSeedPlans}
            disabled={isSeeding}
            variant={plans.length > 0 ? 'outline' : 'default'}
          >
            {isSeeding ? 'Seeding...' : 'Seed Plans'}
          </Button>
        </div>

        {plans.length > 0 && (
          <div className="space-y-2">
            <p className="font-medium text-sm">Existing Plans:</p>
            {plans.map(plan => (
              <div
                key={plan.id}
                className="flex justify-between items-center text-sm"
              >
                <span>{plan.name}</span>
                <span>
                  ${plan.price}/{plan.interval}
                </span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
