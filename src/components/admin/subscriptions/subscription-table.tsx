import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  getStatusBadgeVariant,
  getStatusLabel,
} from '@/lib/schemas/subscription-schema';
import { Subscription } from '@/supabase/types';
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  CreditCard,
  Edit,
  Eye,
  MoreHorizontal,
  Pause,
  User,
  XCircle,
} from 'lucide-react';
import React from 'react';

interface SubscriptionTableProps {
  subscriptions: Subscription[];
  onView: (subscription: Subscription) => void;
  onEdit: (subscription: Subscription) => void;
}

const SubscriptionTable: React.FC<SubscriptionTableProps> = ({
  subscriptions,
  onView,
  onEdit,
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'canceled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-600" />;
      case 'trialing':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'past_due':
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default:
        return <CreditCard className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatPeriod = (start: string, end: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    const now = new Date();

    const isExpired = endDate < now;
    const isEndingSoon =
      endDate.getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000 && !isExpired;

    return (
      <div className="space-y-1">
        <div className="text-sm">
          {startDate.toLocaleDateString()} - {endDate.toLocaleDateString()}
        </div>
        {isExpired && (
          <Badge variant="destructive" className="text-xs">
            Expired
          </Badge>
        )}
        {isEndingSoon && (
          <Badge variant="outline" className="text-xs">
            Ending Soon
          </Badge>
        )}
      </div>
    );
  };

  if (subscriptions.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <CreditCard className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-center mb-2">
            No Subscriptions Found
          </h3>
          <p className="text-muted-foreground text-center max-w-md">
            Customer subscriptions will appear here when they subscribe to your
            plans.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Customer Subscriptions</CardTitle>
            <CardDescription>
              Manage and monitor customer subscription statuses
            </CardDescription>
          </div>
          <Badge variant="outline" className="text-sm">
            {subscriptions.length}{' '}
            {subscriptions.length === 1 ? 'subscription' : 'subscriptions'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Subscription ID</TableHead>
                <TableHead>User ID</TableHead>
                <TableHead>Plan ID</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Current Period</TableHead>
                <TableHead>Trial</TableHead>
                <TableHead className="w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {subscriptions.map(subscription => {
                return (
                  <TableRow key={subscription.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">#{subscription.id}</div>
                        {subscription.created_at && (
                          <div className="text-xs text-muted-foreground">
                            Created:{' '}
                            {new Date(
                              subscription.created_at
                            ).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="font-mono text-sm">
                          {subscription.user_id}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        <span className="font-mono text-sm">
                          #{subscription.plan_id}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(subscription.status)}
                          <Badge
                            variant={getStatusBadgeVariant(subscription.status)}
                          >
                            {getStatusLabel(subscription.status)}
                          </Badge>
                        </div>
                        {subscription.cancel_at_period_end && (
                          <Badge variant="outline" className="text-xs">
                            Cancels at period end
                          </Badge>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      {formatPeriod(
                        subscription.current_period_start,
                        subscription.current_period_end
                      )}
                    </TableCell>

                    <TableCell>
                      {subscription.trial_start && subscription.trial_end ? (
                        <div className="space-y-1">
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            Trial period
                          </div>
                          <div className="text-sm">
                            {new Date(
                              subscription.trial_start
                            ).toLocaleDateString()}{' '}
                            -{' '}
                            {new Date(
                              subscription.trial_end
                            ).toLocaleDateString()}
                          </div>
                        </div>
                      ) : (
                        <span className="text-xs text-muted-foreground">
                          No trial
                        </span>
                      )}
                    </TableCell>

                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => onView(subscription)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onEdit(subscription)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Subscription
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubscriptionTable;
