import { CardWrapper } from '@/components/common/card-wrapper';
import { FormDateField } from '@/components/forms/FormDateField';
import { FormSelectField } from '@/components/forms/FormSelectField';
import { FormSwitchField } from '@/components/forms/FormSwitchField';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  SUBSCRIPTION_STATUSES,
  SubscriptionUpdateData,
  canCancelSubscription,
  getStatusBadgeVariant,
  subscriptionUpdateSchema,
} from '@/lib/schemas/subscription-schema';
import { Subscription } from '@/supabase/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { Info, Loader2 } from 'lucide-react';
import React, { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

interface SubscriptionFormProps {
  subscription?: Subscription;
  onSave: (data: SubscriptionUpdateData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const SubscriptionForm: React.FC<SubscriptionFormProps> = ({
  subscription,
  onSave,
  onCancel,
  loading = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<SubscriptionUpdateData>({
    resolver: zodResolver(subscriptionUpdateSchema),
    defaultValues: {
      status: subscription?.status || 'active',
      cancel_at_period_end: subscription?.cancel_at_period_end || false,
      trial_start: subscription?.trial_start
        ? new Date(subscription.trial_start).toISOString().split('T')[0]
        : '',
      trial_end: subscription?.trial_end
        ? new Date(subscription.trial_end).toISOString().split('T')[0]
        : '',
      current_period_start: subscription?.current_period_start
        ? new Date(subscription.current_period_start)
            .toISOString()
            .split('T')[0]
        : new Date().toISOString().split('T')[0],
      current_period_end: subscription?.current_period_end
        ? new Date(subscription.current_period_end).toISOString().split('T')[0]
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split('T')[0],
    },
  });

  const currentStatus = form.watch('status');
  const cancelAtPeriodEnd = form.watch('cancel_at_period_end');

  const handleSubmit = async (data: SubscriptionUpdateData) => {
    try {
      setIsSubmitting(true);
      await onSave(data);
    } catch (error) {
      console.error('Error saving subscription:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCurrentStatusInfo = () => {
    if (!subscription) return null;

    const status = subscription.status;
    const variant = getStatusBadgeVariant(status);

    return (
      <div className="flex items-center gap-2">
        <Badge variant={variant}>{status}</Badge>
        {subscription.cancel_at_period_end && (
          <Badge variant="outline">Cancels at period end</Badge>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Current Status Info */}
          {subscription && (
            <CardWrapper
              title="Subscription Overview"
              description="Current subscription information and status"
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">
                    Current Status
                  </div>
                  {getCurrentStatusInfo()}
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium text-muted-foreground">
                    Subscription ID
                  </div>
                  <div className="font-mono text-sm">#{subscription.id}</div>
                </div>
                {subscription.created_at && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">
                      Created
                    </div>
                    <div className="text-sm">
                      {new Date(subscription.created_at).toLocaleString()}
                    </div>
                  </div>
                )}
                {subscription.canceled_at && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">
                      Canceled
                    </div>
                    <div className="text-sm">
                      {new Date(subscription.canceled_at).toLocaleString()}
                    </div>
                  </div>
                )}
              </div>
            </CardWrapper>
          )}

          {/* Status Management */}
          <CardWrapper
            title="Status Management"
            description="Update subscription status and cancellation settings"
            className="space-y-4"
          >
            <FormSelectField
              name="status"
              label="Subscription Status"
              description="The current status of this subscription"
              placeholder="Select status"
              options={SUBSCRIPTION_STATUSES.map(status => ({
                value: status.value,
                label: status.label,
              }))}
            />

            <FormSwitchField
              name="cancel_at_period_end"
              label="Cancel at Period End"
              description="Schedule cancellation for the end of current billing period"
              disabled={!canCancelSubscription(currentStatus)}
            />

            {cancelAtPeriodEnd && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  The subscription will be canceled at the end of the current
                  billing period. The customer will retain access until then.
                </AlertDescription>
              </Alert>
            )}
          </CardWrapper>

          {/* Billing Period */}
          <CardWrapper
            title="Billing Period"
            description="Manage current billing period dates"
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormDateField
                name="current_period_start"
                label="Period Start Date"
                description="When the current billing period started"
                required
              />

              <FormDateField
                name="current_period_end"
                label="Period End Date"
                description="When the current billing period ends"
                required
              />
            </div>
          </CardWrapper>

          {/* Trial Period */}
          <CardWrapper
            title="Trial Period"
            description="Manage trial period dates (optional)"
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormDateField
                name="trial_start"
                label="Trial Start Date"
                description="When the trial period started (optional)"
              />

              <FormDateField
                name="trial_end"
                label="Trial End Date"
                description="When the trial period ends (optional)"
              />
            </div>
          </CardWrapper>

          {/* Actions */}
          <div className="flex justify-end gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || loading}
              className="min-w-[120px]"
            >
              {(isSubmitting || loading) && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Update Subscription
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default SubscriptionForm;
