import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Coupon } from '@/supabase/types';
import {
  AlertTriangle,
  Calendar,
  Clock,
  Copy,
  DollarSign,
  Edit,
  Eye,
  MoreHorizontal,
  Percent,
  Ticket,
  Trash2,
  Users,
} from 'lucide-react';
import React from 'react';

interface CouponTableProps {
  coupons: Coupon[];
  onEdit: (coupon: Coupon) => void;
  onDelete: (id: number) => void;
  onView?: (coupon: Coupon) => void;
}

const CouponTable: React.FC<CouponTableProps> = ({
  coupons,
  onEdit,
  onDelete,
  onView,
}) => {
  const formatDiscount = (type: string, value: number) => {
    return type === 'percentage' ? `${value}%` : `$${value.toFixed(2)}`;
  };

  const getStatusBadge = (coupon: Coupon) => {
    const now = new Date();
    const validFrom = coupon.valid_from ? new Date(coupon.valid_from) : null;
    const validUntil = coupon.valid_until ? new Date(coupon.valid_until) : null;

    if (!coupon.is_active) {
      return <Badge variant="secondary">Inactive</Badge>;
    }

    if (validFrom && now < validFrom) {
      return <Badge variant="outline">Scheduled</Badge>;
    }

    if (validUntil && now > validUntil) {
      return <Badge variant="destructive">Expired</Badge>;
    }

    if (
      coupon.max_uses &&
      coupon.current_uses &&
      coupon.current_uses >= coupon.max_uses
    ) {
      return <Badge variant="destructive">Limit Reached</Badge>;
    }

    return <Badge variant="default">Active</Badge>;
  };

  const getUsageInfo = (coupon: Coupon) => {
    const current = coupon.current_uses || 0;
    const max = coupon.max_uses;

    if (!max) return `${current} uses`;

    const percentage = (current / max) * 100;
    const isNearLimit = percentage >= 80;

    return (
      <div className="flex items-center gap-2">
        <span>
          {current} / {max}
        </span>
        {isNearLimit && <AlertTriangle className="h-3 w-3 text-amber-500" />}
      </div>
    );
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (coupons.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Ticket className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-center mb-2">
            No Coupons Found
          </h3>
          <p className="text-muted-foreground text-center max-w-md">
            Create your first coupon to start offering discounts to your
            customers.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Discount Coupons</CardTitle>
            <CardDescription>
              Manage your promotional coupons and discount codes
            </CardDescription>
          </div>
          <Badge variant="outline" className="text-sm">
            {coupons.length} {coupons.length === 1 ? 'coupon' : 'coupons'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Coupon Code</TableHead>
                <TableHead>Discount</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead>Validity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {coupons.map(coupon => (
                <TableRow key={coupon.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <code className="font-mono font-semibold bg-muted px-2 py-1 rounded text-sm">
                          {coupon.code}
                        </code>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => copyToClipboard(coupon.code)}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Copy code</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      {coupon.description && (
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {coupon.description}
                        </p>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center gap-2">
                      {coupon.discount_type === 'percentage' ? (
                        <Percent className="h-4 w-4 text-green-600" />
                      ) : (
                        <DollarSign className="h-4 w-4 text-blue-600" />
                      )}
                      <div>
                        <div className="font-medium">
                          {formatDiscount(
                            coupon.discount_type,
                            coupon.discount_value
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground capitalize">
                          {coupon.discount_type}
                        </div>
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm">{getUsageInfo(coupon)}</div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      {coupon.valid_from && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          From{' '}
                          {new Date(coupon.valid_from).toLocaleDateString()}
                        </div>
                      )}
                      {coupon.valid_until && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          Until{' '}
                          {new Date(coupon.valid_until).toLocaleDateString()}
                        </div>
                      )}
                      {!coupon.valid_from && !coupon.valid_until && (
                        <span className="text-xs text-muted-foreground">
                          No expiry
                        </span>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>{getStatusBadge(coupon)}</TableCell>

                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => copyToClipboard(coupon.code)}
                        >
                          <Copy className="mr-2 h-4 w-4" />
                          Copy Code
                        </DropdownMenuItem>
                        {onView && (
                          <>
                            <DropdownMenuItem onClick={() => onView(coupon)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                          </>
                        )}
                        <DropdownMenuItem onClick={() => onEdit(coupon)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Coupon
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => onDelete(coupon.id)}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Coupon
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default CouponTable;
