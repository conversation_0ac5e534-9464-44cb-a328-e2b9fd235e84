import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
  COUPON_TEMPLATES,
  CouponFormData,
  couponSchema,
  DISCOUNT_TYPES,
} from '@/lib/schemas/coupon-schema';
import { Coupon } from '@/supabase/types';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Calendar,
  DollarSign,
  Info,
  Loader2,
  Percent,
  <PERSON><PERSON>les,
  Ticket,
  <PERSON>,
} from 'lucide-react';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';

interface CouponFormProps {
  coupon?: Coupon;
  onSave: (data: CouponFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const CouponForm: React.FC<CouponFormProps> = ({
  coupon,
  onSave,
  onCancel,
  loading = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showTemplates, setShowTemplates] = useState(!coupon);

  const form = useForm<CouponFormData>({
    resolver: zodResolver(couponSchema),
    defaultValues: {
      code: coupon?.code || '',
      description: coupon?.description || '',
      discount_type:
        (coupon?.discount_type as 'percentage' | 'fixed') || 'percentage',
      discount_value: coupon?.discount_value || 0,
      is_active: coupon?.is_active ?? true,
      max_uses: coupon?.max_uses || null,
      current_uses: coupon?.current_uses || 0,
      valid_from: coupon?.valid_from
        ? new Date(coupon.valid_from).toISOString().split('T')[0]
        : '',
      valid_until: coupon?.valid_until
        ? new Date(coupon.valid_until).toISOString().split('T')[0]
        : '',
      minimum_order_amount: null,
      usage_limit_per_user: null,
      applicable_plans: [],
    },
  });

  const discountType = form.watch('discount_type');
  const discountValue = form.watch('discount_value');

  const handleSubmit = async (data: CouponFormData) => {
    try {
      setIsSubmitting(true);
      await onSave(data);
    } catch (error) {
      console.error('Error saving coupon:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const applyTemplate = (template: (typeof COUPON_TEMPLATES)[number]) => {
    form.setValue('code', template.code);
    form.setValue('description', template.description);
    form.setValue('discount_type', template.discount_type);
    form.setValue('discount_value', template.discount_value);
    form.setValue('max_uses', template.max_uses);
    setShowTemplates(false);
  };

  const generateCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setValue('code', result);
  };

  const formatDiscountPreview = () => {
    if (!discountValue) return 'Enter discount value';
    return discountType === 'percentage'
      ? `${discountValue}% off`
      : `$${discountValue.toFixed(2)} off`;
  };

  return (
    <div className="space-y-6">
      {/* Templates Section */}
      {showTemplates && !coupon && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Quick Templates
            </CardTitle>
            <CardDescription>
              Start with a pre-configured coupon template
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {COUPON_TEMPLATES.map(template => (
                <Button
                  key={template.code}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start"
                  onClick={() => applyTemplate(template)}
                >
                  <div className="font-medium">{template.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {template.code}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {template.discount_type === 'percentage'
                      ? `${template.discount_value}% off`
                      : `$${template.discount_value} off`}
                  </div>
                </Button>
              ))}
            </div>
            <Separator className="my-4" />
            <Button
              variant="ghost"
              onClick={() => setShowTemplates(false)}
              className="w-full"
            >
              Create Custom Coupon
            </Button>
          </CardContent>
        </Card>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Ticket className="h-5 w-5" />
                Coupon Details
              </CardTitle>
              <CardDescription>
                Basic information about your coupon
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Coupon Code</FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input
                              placeholder="e.g., SAVE20"
                              {...field}
                              className="uppercase"
                              onChange={e =>
                                field.onChange(e.target.value.toUpperCase())
                              }
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={generateCode}
                          >
                            Generate
                          </Button>
                        </div>
                        <FormDescription>
                          Unique code customers will enter (uppercase letters,
                          numbers, hyphens, underscores only)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex items-end">
                  <div className="w-full p-4 border rounded-lg bg-muted/50">
                    <div className="text-sm font-medium">Preview</div>
                    <div className="text-lg font-bold text-primary">
                      {formatDiscountPreview()}
                    </div>
                  </div>
                </div>
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Brief description of this coupon..."
                        className="min-h-[80px]"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Discount Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {discountType === 'percentage' ? (
                  <Percent className="h-5 w-5" />
                ) : (
                  <DollarSign className="h-5 w-5" />
                )}
                Discount Configuration
              </CardTitle>
              <CardDescription>Set the discount type and value</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="discount_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Discount Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select discount type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {DISCOUNT_TYPES.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              <div className="flex items-center gap-2">
                                <span className="font-mono text-sm">
                                  {type.icon}
                                </span>
                                {type.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="discount_value"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Discount Value{' '}
                        {discountType === 'percentage' ? '(%)' : '($)'}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step={discountType === 'percentage' ? '1' : '0.01'}
                          min="0"
                          max={
                            discountType === 'percentage' ? '100' : undefined
                          }
                          placeholder={
                            discountType === 'percentage'
                              ? 'e.g., 20'
                              : 'e.g., 10.00'
                          }
                          {...field}
                          onChange={e =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      {discountType === 'percentage' && (
                        <FormDescription>
                          Percentage discount (1-100%)
                        </FormDescription>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="minimum_order_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Order Amount ($)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="Optional minimum order amount"
                        value={field.value || ''}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? parseFloat(e.target.value) : null
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      Minimum order amount required to use this coupon
                      (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Usage Limits */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Usage Limits
              </CardTitle>
              <CardDescription>
                Control how many times this coupon can be used
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="max_uses"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maximum Total Uses</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="Unlimited"
                          value={field.value || ''}
                          onChange={e =>
                            field.onChange(
                              e.target.value ? parseInt(e.target.value) : null
                            )
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Total number of times this coupon can be used (leave
                        empty for unlimited)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="usage_limit_per_user"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Uses Per Customer</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="Unlimited"
                          value={field.value || ''}
                          onChange={e =>
                            field.onChange(
                              e.target.value ? parseInt(e.target.value) : null
                            )
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Maximum uses per customer (leave empty for unlimited)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {coupon && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Current usage: {coupon.current_uses || 0} /{' '}
                    {coupon.max_uses || 'Unlimited'}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Validity Period */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Validity Period
              </CardTitle>
              <CardDescription>Set when this coupon is valid</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="valid_from"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valid From</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        When this coupon becomes active (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="valid_until"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valid Until</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        When this coupon expires (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Coupon Settings</CardTitle>
              <CardDescription>
                Configure coupon behavior and visibility
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Coupon</FormLabel>
                      <FormDescription>
                        Enable this coupon for customer use
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || loading}
              className="min-w-[120px]"
            >
              {(isSubmitting || loading) && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {coupon ? 'Update Coupon' : 'Create Coupon'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CouponForm;
