import { DataTable } from '@/components/data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Website } from '@/supabase/types';
import { ColumnDef } from '@tanstack/react-table';
import { EyeIcon, PencilIcon, Trash2Icon } from 'lucide-react';
import React from 'react';

interface WebsiteTableProps {
  websites: Website[];
  onView: (website: Website) => void;
  onEdit: (website: Website) => void;
  onDelete: (website: Website) => void;
}

const WebsiteTable: React.FC<WebsiteTableProps> = ({
  websites,
  onView,
  onEdit,
  onDelete,
}) => {
  const columns: ColumnDef<Website>[] = [
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }) => (
        <div className="font-medium">#{row.getValue('id')}</div>
      ),
    },
    {
      accessorKey: 'domain_name',
      header: 'Domain Name',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('domain_name')}</div>
      ),
    },
    {
      accessorKey: 'website_url',
      header: 'URL',
      cell: ({ row }) => (
        <a
          href={row.getValue('website_url') as string}
          target="_blank"
          rel="noopener noreferrer"
          className="text-primary hover:underline"
        >
          {row.getValue('website_url')}
        </a>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        return (
          <Badge
            variant={
              status === 'active'
                ? 'success'
                : status === 'inactive'
                  ? 'destructive'
                  : 'default'
            }
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Created At',
      cell: ({ row }) => {
        const date = row.getValue('created_at') as string;
        return <div>{new Date(date).toLocaleDateString()}</div>;
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const website = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onView(website)}
              className="h-8 w-8"
            >
              <EyeIcon className="h-4 w-4" />
              <span className="sr-only">View</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onEdit(website)}
              className="h-8 w-8"
            >
              <PencilIcon className="h-4 w-4" />
              <span className="sr-only">Edit</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDelete(website)}
              className="h-8 w-8 text-destructive hover:text-destructive/90"
            >
              <Trash2Icon className="h-4 w-4" />
              <span className="sr-only">Delete</span>
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={websites}
      searchKey="domain_name"
      filterableColumns={[
        {
          id: 'status',
          title: 'Status',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Inactive', value: 'inactive' },
            { label: 'Pending', value: 'pending' },
          ],
        },
      ]}
    />
  );
};

export default WebsiteTable;
