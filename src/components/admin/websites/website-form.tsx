import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { InsertWebsite, Website } from '@/supabase/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const websiteFormSchema = z.object({
  domain_name: z
    .string()
    .min(1, 'Domain name is required')
    .regex(
      /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
      'Please enter a valid domain (e.g., example.com)'
    ),
  website_url: z
    .string()
    .min(1, 'Website URL is required')
    .url('Must be a valid URL'),
  status: z.enum(['active', 'inactive', 'pending']),
  website_description: z.string().optional(),
  website_niche: z.string().optional(),
  hosting_provider: z.string().optional(),
  wordpress_id: z.string().optional(),
  wordpress_pass: z.string().optional(),
  ssl_enabled: z.boolean().default(false),
  user_id: z.string().min(1, 'User ID is required'),
});

type WebsiteFormValues = z.infer<typeof websiteFormSchema>;

interface WebsiteFormProps {
  initialData?: Website;
  onSubmit: (data: InsertWebsite) => Promise<void>;
  loading: boolean;
  users: { id: string; full_name: string; email: string }[];
}

export const WebsiteForm: React.FC<WebsiteFormProps> = ({
  initialData,
  onSubmit,
  loading,
  users,
}) => {
  const form = useForm<WebsiteFormValues>({
    resolver: zodResolver(websiteFormSchema),
    defaultValues: {
      domain_name: initialData?.domain_name || '',
      website_url: initialData?.website_url || '',
      status:
        (initialData?.status as 'active' | 'inactive' | 'pending') || 'pending',
      website_description: initialData?.website_description || '',
      website_niche: initialData?.website_niche || '',
      hosting_provider: initialData?.hosting_provider || '',
      wordpress_id: initialData?.wordpress_id || '',
      wordpress_pass: initialData?.wordpress_pass || '',
      ssl_enabled: initialData?.ssl_enabled || false,
      user_id: initialData?.user_id || '',
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset({
        domain_name: initialData.domain_name,
        website_url: initialData.website_url,
        status:
          (initialData.status as 'active' | 'inactive' | 'pending') ||
          'pending',
        website_description: initialData.website_description || '',
        website_niche: initialData.website_niche || '',
        hosting_provider: initialData.hosting_provider || '',
        wordpress_id: initialData.wordpress_id || '',
        wordpress_pass: initialData.wordpress_pass || '',
        ssl_enabled: initialData.ssl_enabled || false,
        user_id: initialData.user_id,
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (values: WebsiteFormValues) => {
    await onSubmit({
      domain_name: values.domain_name,
      website_url: values.website_url,
      status: values.status,
      website_description: values.website_description || null,
      website_niche: values.website_niche || null,
      hosting_provider: values.hosting_provider || null,
      wordpress_id: values.wordpress_id || null,
      wordpress_pass: values.wordpress_pass || null,
      ssl_enabled: values.ssl_enabled,
      user_id: values.user_id,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="domain_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Domain Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="example.com" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="website_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website URL</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="https://example.com" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="user_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>User</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select user" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.full_name} ({user.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="website_description"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Website Description (optional)</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Enter a description of the website"
                    rows={3}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="website_niche"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website Niche (optional)</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="e.g., Technology, Health, Finance"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="hosting_provider"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Hosting Provider (optional)</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="e.g., AWS, GoDaddy, DigitalOcean"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="wordpress_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>WordPress Username (optional)</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="admin" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="wordpress_pass"
            render={({ field }) => (
              <FormItem>
                <FormLabel>WordPress Password (optional)</FormLabel>
                <FormControl>
                  <Input {...field} type="password" placeholder="••••••••" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="ssl_enabled"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>SSL Enabled</FormLabel>
                </div>
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" disabled={loading} className="w-full md:w-auto">
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {initialData ? 'Update Website' : 'Create Website'}
        </Button>
      </form>
    </Form>
  );
};

export default WebsiteForm;
