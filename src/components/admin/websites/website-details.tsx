import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Website } from '@/supabase/types';

interface WebsiteDetailsProps {
  open: boolean;
  onClose: () => void;
  website: Website;
}

export const WebsiteDetails: React.FC<WebsiteDetailsProps> = ({
  open,
  onClose,
  website,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'inactive':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Website Details</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">
                Website ID
              </span>
              <span className="font-bold">#{website.id}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">Name</span>
              <span className="font-bold">
                {website.domain_name.split('.')[0]}
              </span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">URL</span>
              <a
                href={website.website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                {website.website_url}
              </a>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">
                Domain Name
              </span>
              <span>{website.domain_name}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">Status</span>
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(
                  website.status || ''
                )}`}
              >
                {website.status}
              </span>
            </div>

            {website.website_niche && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">Niche</span>
                <span>{website.website_niche}</span>
              </div>
            )}

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">
                SSL Enabled
              </span>
              <span>{website.ssl_enabled ? 'Yes' : 'No'}</span>
            </div>

            {website.ssl_expiry_date && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  SSL Expiry Date
                </span>
                <span>
                  {new Date(website.ssl_expiry_date).toLocaleDateString()}
                </span>
              </div>
            )}

            {website.hosting_provider && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Hosting Provider
                </span>
                <span>{website.hosting_provider}</span>
              </div>
            )}

            {website.wordpress_id && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  WordPress Username
                </span>
                <span>{website.wordpress_id}</span>
              </div>
            )}

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">
                Created At
              </span>
              <span>
                {new Date(website.created_at || '').toLocaleDateString()}
              </span>
            </div>

            {website.updated_at && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Last Updated
                </span>
                <span>{new Date(website.updated_at).toLocaleDateString()}</span>
              </div>
            )}

            {website.website_description && (
              <div className="border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Description
                </span>
                <p className="mt-2">{website.website_description}</p>
              </div>
            )}

            {website.website_images && website.website_images.length > 0 && (
              <div className="border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Images
                </span>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-2">
                  {website.website_images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`Website screenshot ${index + 1}`}
                      className="rounded-md border h-24 w-full object-cover"
                    />
                  ))}
                </div>
              </div>
            )}

            {website.hosting_credentials && (
              <div className="border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Hosting Credentials
                </span>
                <pre className="mt-2 p-2 bg-muted rounded-md text-xs overflow-auto">
                  {JSON.stringify(website.hosting_credentials, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WebsiteDetails;
