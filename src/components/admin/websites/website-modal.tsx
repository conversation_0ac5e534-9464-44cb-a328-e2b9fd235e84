import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { InsertWebsite, Website } from '@/supabase/types';
import { WebsiteForm } from './website-form';

interface WebsiteModalProps {
  open: boolean;
  onClose: () => void;
  website?: Website;
  onSubmit: (data: InsertWebsite) => Promise<void>;
  loading: boolean;
  users: { id: string; full_name: string; email: string }[];
}

export const WebsiteModal: React.FC<WebsiteModalProps> = ({
  open,
  onClose,
  website,
  onSubmit,
  loading,
  users,
}) => {
  const handleSubmit = async (data: InsertWebsite) => {
    await onSubmit(data);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {website ? 'Edit Website' : 'Create Website'}
          </DialogTitle>
          <DialogDescription>
            {website
              ? 'Update website details'
              : 'Fill out the form to create a new website'}
          </DialogDescription>
        </DialogHeader>

        <WebsiteForm
          initialData={website}
          onSubmit={handleSubmit}
          loading={loading}
          users={users}
        />
      </DialogContent>
    </Dialog>
  );
};

export default WebsiteModal;
