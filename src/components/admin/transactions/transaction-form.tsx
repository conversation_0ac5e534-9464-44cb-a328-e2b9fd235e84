import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { InsertTransaction, Transaction } from '@/supabase/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const transactionFormSchema = z.object({
  amount: z
    .string()
    .min(1, 'Amount is required')
    .refine(
      val => !isNaN(parseFloat(val)) && parseFloat(val) > 0,
      'Amount must be a positive number'
    ),
  currency: z.string().default('USD'),
  status: z.enum(['completed', 'pending', 'failed', 'refunded']),
  payment_method: z.enum(['credit_card', 'paypal', 'bank_transfer']),
  payment_provider: z.string().optional(),
  payment_id: z.string().optional(),
  invoice_url: z.string().url().optional().or(z.literal('')),
  metadata: z.string().optional(),
  user_id: z.string().min(1, 'User ID is required'),
  subscription_id: z.string().optional(),
  coupon_id: z.string().optional(),
});

type TransactionFormValues = z.infer<typeof transactionFormSchema>;

interface TransactionFormProps {
  initialData?: Transaction;
  onSubmit: (data: InsertTransaction) => Promise<void>;
  loading: boolean;
  users: { id: string; full_name: string; email: string }[];
  subscriptions?: { id: number; name: string }[];
  coupons?: { id: number; code: string }[];
}

export const TransactionForm: React.FC<TransactionFormProps> = ({
  initialData,
  onSubmit,
  loading,
  users,
  subscriptions,
  coupons,
}) => {
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionFormSchema),
    defaultValues: {
      amount: initialData ? (initialData.amount / 100).toString() : '',
      currency: initialData?.currency || 'USD',
      status:
        (initialData?.status as
          | 'completed'
          | 'pending'
          | 'failed'
          | 'refunded') || 'pending',
      payment_method:
        (initialData?.payment_method as
          | 'credit_card'
          | 'paypal'
          | 'bank_transfer') || 'credit_card',
      payment_provider: initialData?.payment_provider || '',
      payment_id: initialData?.payment_id || '',
      invoice_url: initialData?.invoice_url || '',
      metadata: initialData?.metadata
        ? JSON.stringify(initialData.metadata)
        : '',
      user_id: initialData?.user_id || '',
      subscription_id: initialData?.subscription_id?.toString() || '',
      coupon_id: initialData?.coupon_id?.toString() || '',
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset({
        amount: (initialData.amount / 100).toString(),
        currency: initialData.currency || 'USD',
        status: initialData.status as
          | 'completed'
          | 'pending'
          | 'failed'
          | 'refunded',
        payment_method: initialData.payment_method as
          | 'credit_card'
          | 'paypal'
          | 'bank_transfer',
        payment_provider: initialData.payment_provider || '',
        payment_id: initialData.payment_id || '',
        invoice_url: initialData.invoice_url || '',
        metadata: initialData.metadata
          ? JSON.stringify(initialData.metadata)
          : '',
        user_id: initialData.user_id,
        subscription_id: initialData.subscription_id?.toString() || '',
        coupon_id: initialData.coupon_id?.toString() || '',
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (values: TransactionFormValues) => {
    const amountInCents = Math.round(parseFloat(values.amount) * 100);

    await onSubmit({
      amount: amountInCents,
      currency: values.currency,
      status: values.status,
      payment_method: values.payment_method,
      payment_provider: values.payment_provider || null,
      payment_id: values.payment_id || null,
      invoice_url: values.invoice_url || null,
      metadata: values.metadata ? JSON.parse(values.metadata) : null,
      user_id: values.user_id,
      subscription_id: values.subscription_id
        ? parseInt(values.subscription_id)
        : null,
      coupon_id: values.coupon_id ? parseInt(values.coupon_id) : null,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Amount</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="0.00"
                    type="number"
                    step="0.01"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                      <SelectItem value="refunded">Refunded</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="payment_method"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Method</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="credit_card">Credit Card</SelectItem>
                      <SelectItem value="paypal">PayPal</SelectItem>
                      <SelectItem value="bank_transfer">
                        Bank Transfer
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="payment_provider"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Provider (optional)</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="e.g., Stripe, PayPal" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="payment_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment ID (optional)</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="e.g., ch_1234567890" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="invoice_url"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Invoice URL (optional)</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="https://example.com/invoice" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="metadata"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Metadata (JSON, optional)</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Enter JSON metadata"
                    rows={3}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="user_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>User</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select user" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.full_name} ({user.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {subscriptions && (
            <FormField
              control={form.control}
              name="subscription_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Subscription (optional)</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select subscription" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">None</SelectItem>
                        {subscriptions.map(subscription => (
                          <SelectItem
                            key={subscription.id}
                            value={subscription.id.toString()}
                          >
                            {subscription.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {coupons && (
            <FormField
              control={form.control}
              name="coupon_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Coupon (optional)</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select coupon" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">None</SelectItem>
                        {coupons.map(coupon => (
                          <SelectItem
                            key={coupon.id}
                            value={coupon.id.toString()}
                          >
                            {coupon.code}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <Button type="submit" disabled={loading} className="w-full md:w-auto">
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {initialData ? 'Update Transaction' : 'Create Transaction'}
        </Button>
      </form>
    </Form>
  );
};

export default TransactionForm;
