import { DataTable } from '@/components/data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Transaction } from '@/supabase/types';
import { formatCurrency } from '@/utils/format';
import { ColumnDef } from '@tanstack/react-table';
import { EyeIcon, ReceiptIcon } from 'lucide-react';
import React from 'react';

interface TransactionTableProps {
  transactions: Transaction[];
  onView: (transaction: Transaction) => void;
  onViewReceipt: (transaction: Transaction) => void;
  profiles?: Record<string, { full_name: string; email: string }>;
}

const TransactionTable: React.FC<TransactionTableProps> = ({
  transactions,
  onView,
  onViewReceipt,
  profiles,
}) => {
  const columns: ColumnDef<Transaction>[] = [
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }) => (
        <div className="font-medium">#{row.getValue('id')}</div>
      ),
    },
    {
      accessorKey: 'user_id',
      header: 'User',
      cell: ({ row }) => {
        const userId = row.getValue('user_id') as string;
        const user = profiles?.[userId];

        return (
          <div className="flex flex-col">
            <span className="font-medium">{user?.full_name || 'N/A'}</span>
            <span className="text-muted-foreground text-sm">
              {user?.email || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'amount',
      header: 'Amount',
      cell: ({ row }) => {
        const amount = row.getValue('amount') as number;
        const currency = row.original.currency || 'USD';

        return (
          <div className="font-medium">
            {formatCurrency(amount / 100, currency)}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;

        return (
          <Badge
            variant={
              status === 'completed'
                ? 'success'
                : status === 'failed'
                  ? 'destructive'
                  : 'default'
            }
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'payment_method',
      header: 'Payment Method',
      cell: ({ row }) => {
        const method = row.getValue('payment_method') as string;
        return <div className="capitalize">{method || 'N/A'}</div>;
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Date',
      cell: ({ row }) => {
        const date = row.getValue('created_at') as string;
        return <div>{new Date(date).toLocaleDateString()}</div>;
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const transaction = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onView(transaction)}
              className="h-8 w-8"
            >
              <EyeIcon className="h-4 w-4" />
              <span className="sr-only">View</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onViewReceipt(transaction)}
              className="h-8 w-8"
              disabled={
                transaction.status !== 'completed' || !transaction.invoice_url
              }
            >
              <ReceiptIcon className="h-4 w-4" />
              <span className="sr-only">View Receipt</span>
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={transactions}
      searchKey="id"
      filterableColumns={[
        {
          id: 'status',
          title: 'Status',
          options: [
            { label: 'Completed', value: 'completed' },
            { label: 'Failed', value: 'failed' },
            { label: 'Pending', value: 'pending' },
            { label: 'Refunded', value: 'refunded' },
          ],
        },
        {
          id: 'payment_method',
          title: 'Payment Method',
          options: [
            { label: 'Credit Card', value: 'credit_card' },
            { label: 'PayPal', value: 'paypal' },
            { label: 'Bank Transfer', value: 'bank_transfer' },
          ],
        },
      ]}
    />
  );
};

export default TransactionTable;
