import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { InsertTransaction, Transaction } from '@/supabase/types';
import { TransactionForm } from './transaction-form';

interface TransactionModalProps {
  open: boolean;
  onClose: () => void;
  transaction?: Transaction;
  onSubmit: (data: InsertTransaction) => Promise<void>;
  loading: boolean;
  users: { id: string; full_name: string; email: string }[];
  subscriptions?: { id: number; name: string }[];
  coupons?: { id: number; code: string }[];
}

export const TransactionModal: React.FC<TransactionModalProps> = ({
  open,
  onClose,
  transaction,
  onSubmit,
  loading,
  users,
  subscriptions,
  coupons,
}) => {
  const handleSubmit = async (data: InsertTransaction) => {
    await onSubmit(data);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {transaction ? 'Edit Transaction' : 'Create Transaction'}
          </DialogTitle>
          <DialogDescription>
            {transaction
              ? 'Update transaction details'
              : 'Fill out the form to create a new transaction'}
          </DialogDescription>
        </DialogHeader>

        <TransactionForm
          initialData={transaction}
          onSubmit={handleSubmit}
          loading={loading}
          users={users}
          subscriptions={subscriptions}
          coupons={coupons}
        />
      </DialogContent>
    </Dialog>
  );
};

export default TransactionModal;
