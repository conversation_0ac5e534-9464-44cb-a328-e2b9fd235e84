import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Transaction } from '@/supabase/types';
import { formatCurrency } from '../../../utils/format';

interface TransactionDetailsProps {
  open: boolean;
  onClose: () => void;
  transaction: Transaction;
  profile?: { full_name: string; email: string } | null;
}

export const TransactionDetails: React.FC<TransactionDetailsProps> = ({
  open,
  onClose,
  transaction,
  profile,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'refunded':
        return 'text-amber-600 bg-amber-50 border-amber-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Transaction Details</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">
                Transaction ID
              </span>
              <span className="font-bold">#{transaction.id}</span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">Amount</span>
              <span className="font-bold">
                {formatCurrency(transaction.amount / 100, transaction.currency)}
              </span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">Status</span>
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(
                  transaction.status
                )}`}
              >
                {transaction.status}
              </span>
            </div>

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">
                Payment Method
              </span>
              <span className="capitalize">
                {transaction.payment_method || 'N/A'}
              </span>
            </div>

            {transaction.payment_provider && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Payment Provider
                </span>
                <span>{transaction.payment_provider}</span>
              </div>
            )}

            {transaction.payment_id && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Payment ID
                </span>
                <span>{transaction.payment_id}</span>
              </div>
            )}

            <div className="flex justify-between border-b pb-2">
              <span className="font-medium text-muted-foreground">Date</span>
              <span>
                {new Date(transaction.created_at || '').toLocaleString()}
              </span>
            </div>

            {transaction.subscription_id && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Subscription ID
                </span>
                <span>#{transaction.subscription_id}</span>
              </div>
            )}

            {transaction.coupon_id && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Coupon ID
                </span>
                <span>#{transaction.coupon_id}</span>
              </div>
            )}

            {profile && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">User</span>
                <div className="text-right">
                  <div>{profile.full_name}</div>
                  <div className="text-sm text-muted-foreground">
                    {profile.email}
                  </div>
                </div>
              </div>
            )}

            {transaction.invoice_url && (
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Invoice
                </span>
                <a
                  href={transaction.invoice_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  View Invoice
                </a>
              </div>
            )}

            {transaction.metadata && (
              <div className="border-b pb-2">
                <span className="font-medium text-muted-foreground">
                  Additional Information
                </span>
                <pre className="mt-2 p-2 bg-muted rounded-md text-xs overflow-auto">
                  {JSON.stringify(transaction.metadata, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TransactionDetails;
