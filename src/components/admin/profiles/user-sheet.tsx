import UserForm from '@/components/admin/profiles/user-form';
import { AdaptiveWrapper } from '@/components/common/adaptive-wrapper';
import { InsertProfile, Profile, UpdateProfile } from '@/supabase/types';
import React from 'react';

interface UserSheetProps {
  isOpen: boolean;
  onClose: () => void;
  user?: Profile;
  onSave: (userData: InsertProfile | UpdateProfile) => Promise<void>;
}

export const UserSheet: React.FC<UserSheetProps> = ({
  isOpen,
  onClose,
  user,
  onSave,
}) => {
  const handleSave = async (userData: InsertProfile | UpdateProfile) => {
    await onSave(userData);
    onClose();
  };

  return (
    <AdaptiveWrapper
      isOpen={isOpen}
      onClose={onClose}
      title={user ? 'Edit User' : 'Add New User'}
      description={
        user
          ? 'Update the user information below.'
          : 'Create a new user account by filling out the form below.'
      }
      size="lg"
    >
      <UserForm user={user} onSave={handleSave} onCancel={onClose} />
    </AdaptiveWrapper>
  );
};
