import UserForm from '@/components/admin/profiles/user-form';
import { Modal } from '@/components/common/modal';
import { InsertProfile, Profile, UpdateProfile } from '@/supabase/types';
import React from 'react';

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user?: Profile;
  onSave: (userData: InsertProfile | UpdateProfile) => Promise<void>;
}

export const UserModal: React.FC<UserModalProps> = ({
  isOpen,
  onClose,
  user,
  onSave,
}) => {
  const handleSave = async (userData: InsertProfile | UpdateProfile) => {
    await onSave(userData);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={user ? 'Edit User' : 'Add New User'}
      description={
        user
          ? 'Update the user information below.'
          : 'Create a new user account by filling out the form below.'
      }
      size="lg"
    >
      <UserForm user={user} onSave={handleSave} onCancel={onClose} />
    </Modal>
  );
};
