import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { OnboardingStep } from '@/types/onboarding';
import { ONBOARDING_STEP_CONFIG } from '@/utils/onboarding-steps';
import { CheckIcon, ChevronRightIcon } from 'lucide-react';

interface SidebarProgressProps {
  currentStep: OnboardingStep;
  steps: OnboardingStep[];
  completedSteps: OnboardingStep[];
  onSelectStep: (step: OnboardingStep) => void;
}

export default function SidebarProgressImproved({
  currentStep,
  steps,
  completedSteps,
  onSelectStep,
}: SidebarProgressProps) {
  const getCurrentStepIndex = () => steps.indexOf(currentStep);
  const getStepIndex = (step: OnboardingStep) => steps.indexOf(step);

  const isCompleted = (step: OnboardingStep) => completedSteps.includes(step);
  const isCurrent = (step: OnboardingStep) => step === currentStep;
  const isAccessible = (step: OnboardingStep) => {
    const stepIndex = getStepIndex(step);
    const currentIndex = getCurrentStepIndex();
    // Allow access to current step and all previous steps, plus next step if current is completed
    return (
      stepIndex <= currentIndex ||
      (stepIndex === currentIndex + 1 && isCompleted(currentStep))
    );
  };

  return (
    <Card className="sticky top-8">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">Setup Progress</CardTitle>
        <div className="text-sm text-muted-foreground">
          Step {getCurrentStepIndex() + 1} of {steps.length}
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {steps.map((step, index) => {
          const config = ONBOARDING_STEP_CONFIG[step];
          const completed = isCompleted(step);
          const current = isCurrent(step);
          const accessible = isAccessible(step);

          return (
            <Button
              key={step}
              variant={current ? 'secondary' : 'ghost'}
              className={cn(
                'w-full justify-start h-auto p-3 text-left',
                completed && 'text-muted-foreground',
                !accessible && 'cursor-not-allowed opacity-50'
              )}
              onClick={() => accessible && onSelectStep(step)}
              disabled={!accessible}
            >
              <div className="flex items-center gap-3 w-full">
                <div
                  className={cn(
                    'flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium border-2',
                    completed
                      ? 'bg-primary text-primary-foreground border-primary'
                      : current
                        ? 'bg-secondary border-primary text-foreground'
                        : 'border-muted-foreground/30 text-muted-foreground'
                  )}
                >
                  {completed ? (
                    <CheckIcon className="w-3 h-3" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div
                    className={cn(
                      'font-medium text-sm',
                      completed && 'line-through',
                      current && 'text-foreground'
                    )}
                  >
                    {config.title}
                  </div>
                  <div className="text-xs text-muted-foreground truncate">
                    {config.description}
                  </div>
                </div>

                {current && (
                  <ChevronRightIcon className="w-4 h-4 text-muted-foreground" />
                )}
              </div>
            </Button>
          );
        })}

        <div className="pt-4 border-t">
          <div className="text-xs text-muted-foreground">
            {completedSteps.length} of {steps.length} steps completed
          </div>
          <div className="w-full bg-muted rounded-full h-2 mt-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{
                width: `${(completedSteps.length / steps.length) * 100}%`,
              }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
