import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  BarChart3,
  CreditCard,
  Globe,
  Package,
  Receipt,
  Settings,
  Tag,
  Users,
} from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

interface AdminSidebarProps {
  className?: string;
}

const navigationItems = [
  { id: 'dashboard', label: 'Dashboard', icon: BarChart3, path: '/admin' },
  { id: 'users', label: 'Users', icon: Users, path: '/admin/users' },
  {
    id: 'subscriptions',
    label: 'Subscriptions',
    icon: CreditCard,
    path: '/admin/subscriptions',
  },
  { id: 'plans', label: 'Plans', icon: Package, path: '/admin/plans' },
  { id: 'coupons', label: 'Coupons', icon: Tag, path: '/admin/coupons' },
  { id: 'websites', label: 'Websites', icon: Globe, path: '/admin/websites' },
  {
    id: 'transactions',
    label: 'Transactions',
    icon: Receipt,
    path: '/admin/transactions',
  },
];

const settingsItems = [
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    path: '/admin/settings',
  },
];

export function AdminSidebar({ className }: AdminSidebarProps) {
  const location = useLocation();
  const navigate = useNavigate();

  return (
    <aside className={cn('bg-card border-r border-border', className)}>
      <div className="flex flex-col h-full">
        <nav className="flex-1 p-4 space-y-2">
          <div className="space-y-2">
            {navigationItems.map(item => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              return (
                <Button
                  key={item.id}
                  variant={isActive ? 'default' : 'ghost'}
                  className="w-full justify-start"
                  onClick={() => navigate(item.path)}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {item.label}
                </Button>
              );
            })}
          </div>
        </nav>

        <div className="border-t p-4 space-y-1">
          {settingsItems.map(item => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            return (
              <Button
                key={item.id}
                variant={isActive ? 'default' : 'ghost'}
                className="w-full justify-start"
                onClick={() => navigate(item.path)}
              >
                <Icon className="mr-2 h-4 w-4" />
                {item.label}
              </Button>
            );
          })}
        </div>
      </div>
    </aside>
  );
}
