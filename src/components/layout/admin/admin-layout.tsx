import { useIsMobile } from '@/hooks/use-mobile';
import React from 'react';
import { AdminHeader, AdminSidebar } from './index';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const isMobile = useIsMobile();

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header - Fixed height */}
      <AdminHeader isMobile={isMobile} onMenuClick={() => {}}>
        <AdminSidebar className="w-64 h-full" />
      </AdminHeader>

      {/* Content area - Takes remaining height */}
      <div className="flex flex-1 overflow-hidden">
        {/* Desktop Sidebar - Fixed width, full height */}
        {!isMobile && (
          <div className="w-64 flex-shrink-0">
            <AdminSidebar className="w-full h-full" />
          </div>
        )}

        {/* Main Content - Scrollable */}
        <main className="flex-1 overflow-auto">
          <div className="p-4 md:p-6">{children}</div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
