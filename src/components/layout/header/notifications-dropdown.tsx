import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { Bell } from 'lucide-react';

interface Notification {
  id: string;
  title: string;
  message: string;
  read: boolean;
}

interface NotificationsDropdownProps {
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
}

export function NotificationsDropdown({
  notifications,
  onMarkAsRead,
}: NotificationsDropdownProps) {
  const unreadNotifications = notifications.filter(n => !n.read).length;

  const handleNotificationClick = (notificationId: string) => {
    onMarkAsRead(notificationId);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative rounded-full hover:bg-accent"
        >
          <Bell className="h-4 w-4" />
          {unreadNotifications > 0 && (
            <Badge className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs bg-red-500 hover:bg-red-500">
              {unreadNotifications}
            </Badge>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">Notifications</p>
            <p className="text-xs leading-none text-muted-foreground">
              {unreadNotifications > 0
                ? `You have ${unreadNotifications} unread messages`
                : 'All caught up!'}
            </p>
          </div>
        </DropdownMenuLabel>
        <Separator />
        <div className="max-h-64 overflow-y-auto">
          {notifications.length > 0 ? (
            notifications.slice(0, 5).map(notification => (
              <DropdownMenuItem
                key={notification.id}
                onClick={() => handleNotificationClick(notification.id)}
                className={`cursor-pointer ${
                  !notification.read
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-l-2 border-blue-500'
                    : 'hover:bg-accent'
                }`}
              >
                <div className="flex flex-col space-y-1 w-full">
                  <p
                    className={`text-sm leading-none ${
                      !notification.read ? 'font-medium' : 'font-normal'
                    }`}
                  >
                    {notification.title}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {notification.message}
                  </p>
                </div>
              </DropdownMenuItem>
            ))
          ) : (
            <DropdownMenuItem disabled>
              <p className="text-sm text-center text-muted-foreground w-full">
                No notifications
              </p>
            </DropdownMenuItem>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
