import { Badge } from '@/components/ui/badge';
import { CreditCard } from 'lucide-react';

interface CreditsBadgeProps {
  credits: number;
}

export function CreditsBadge({ credits }: CreditsBadgeProps) {
  return (
    <Badge
      variant="secondary"
      className="hidden sm:flex rounded-full px-3 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800"
    >
      <CreditCard className="mr-1 h-3 w-3" />
      {credits.toLocaleString()} Credits
    </Badge>
  );
}
