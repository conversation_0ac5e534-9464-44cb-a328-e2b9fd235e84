import { Button } from '@/components/ui/button';
import { BarChart3, FileText, Home, Settings } from 'lucide-react';
import { Link } from 'react-router-dom';

interface DesktopNavigationProps {
  showNav: boolean;
}

export function DesktopNavigation({ showNav }: DesktopNavigationProps) {
  if (!showNav) return null;

  const navigationItems = [
    {
      href: '/dashboard',
      icon: Home,
      label: 'Dashboard',
    },
    {
      href: '/dashboard/writer',
      icon: FileText,
      label: 'AI Writer',
    },
    {
      href: '/dashboard/analytics',
      icon: BarChart3,
      label: 'Analytics',
    },
    {
      href: '/dashboard/settings',
      icon: Settings,
      label: 'Settings',
    },
  ];

  return (
    <nav className="hidden md:flex items-center space-x-1">
      {navigationItems.map(item => {
        const Icon = item.icon;
        return (
          <Link key={item.href} to={item.href}>
            <Button variant="ghost" className="px-3 hover:bg-accent">
              <Icon className="mr-1 h-4 w-4" />
              {item.label}
            </Button>
          </Link>
        );
      })}
    </nav>
  );
}
