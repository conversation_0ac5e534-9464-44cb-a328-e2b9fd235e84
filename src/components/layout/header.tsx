import { useAppStore } from '@/stores';
import {
  CreditsBadge,
  DesktopNavigation,
  Logo,
  MobileNavigation,
  NotificationsDropdown,
  ThemeToggle,
  UserMenu,
} from './header/index';

type HeaderProps = {
  showNav?: boolean;
  showCredits?: boolean;
};

export default function Header({
  showNav = false,
  showCredits = false,
}: HeaderProps) {
  const { user, credits, notifications, markNotificationAsRead, logout } =
    useAppStore();

  const displayUser = user || {
    id: 'guest',
    name: 'Guest User',
    email: '<EMAIL>',
    avatar: '',
    plan: 'free' as const,
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        {/* Mobile Navigation */}
        <MobileNavigation showNav={showNav} />

        {/* Logo */}
        <Logo />

        {/* Desktop Navigation */}
        <DesktopNavigation showNav={showNav} />

        {/* Right Side Actions */}
        <div className="flex flex-1 items-center justify-end space-x-2">
          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Credits and User Actions */}
          {showCredits && (
            <div className="flex items-center space-x-2">
              {/* Credits Badge */}
              <CreditsBadge credits={credits} />

              {/* Notifications */}
              <NotificationsDropdown
                notifications={notifications}
                onMarkAsRead={markNotificationAsRead}
              />

              {/* User Menu */}
              <UserMenu user={displayUser} onLogout={logout} />
            </div>
          )}

          {/* User Menu for non-credits pages */}
          {!showCredits && user && (
            <UserMenu user={displayUser} onLogout={logout} />
          )}
        </div>
      </div>
    </header>
  );
}
