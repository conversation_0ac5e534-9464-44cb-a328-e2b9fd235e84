import { SVGProps } from 'react';

interface NetworkLogoProps extends SVGProps<SVGSVGElement> {
  name: string;
}

export default function NetworkLogo({ name, ...props }: NetworkLogoProps) {
  // Simplified implementation, in a real app you would have more logos
  switch (name.toLowerCase()) {
    case 'notion':
      return (
        <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
          <path d="M4.459 4.208c.746.606 1.026.56 2.428.466l13.215-.793c.28 0 .047-.28-.046-.326L17.86 1.968c-.42-.326-.981-.7-2.055-.607L3.01 2.295c-.466.046-.56.28-.374.466l1.823 1.447zm1.775 2.22c.746.56 1.96.42 1.96.42l13.495-.793c.28-.046.327-.14.187-.326l-1.867-1.54c-.327-.28-1.026-.607-1.913-.56l-13.682.793c-.28 0-.374.28-.187.466l1.497 1.493.513.047zM1.807 6.196c-.28.233-.302.466 0 .699l1.635 1.447c.517.435 1.145.585 2.1.51l14.52-.84c.373-.02.28-.237.187-.37L18.361 5.05c-.31-.282-.666-.427-1.64-.401l-15.03.884zM1.27 17.873c.608 0 .94-.28 1.22-.652l7.347-9.597c.14-.233 0-.233-.187-.046l-5.738 4.196c-.279.233-.49.326-.7 0L.334 8.595c-.186-.326-.326-.28-.326 0l.84 5.05c.187.98.42 1.494.42 4.226z" />
        </svg>
      );
    case 'mongodb':
      return (
        <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
          <path d="M17.193 9.555c-1.264-5.58-4.252-7.414-4.573-8.115-.28-.394-.53-.954-.735-1.44-.036.495-.055.685-.523 1.184-.723.566-4.438 3.682-4.74 10.02-.282 5.912 4.27 9.435 4.888 9.884l.07.05A73.49 73.49 0 0111.91 24h.481c.114-1.032.284-2.056.51-3.07.417-.296.604-.463.85-.693a11.342 11.342 0 003.639-8.464c.01-.814-.103-1.662-.197-2.218zm-5.336 8.195s0-8.291.275-8.29c.213 0 .49 10.695.49 10.695-.381-.045-.765-1.76-.765-2.405z" />
        </svg>
      );
    case 'snowflake':
      return (
        <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
          <path d="M12 0L8.45 3.55 12 7.1l3.55-3.55L12 0zm-9.45 6.55L.55 9l2 2 2-2-2-2.45zm18.9 0l-2 2.45 2 2 2-2-2-2.45zM3.55 12l3.55 3.55L10.65 12 7.1 8.45 3.55 12zm16.9 0l-3.55 3.55L20.45 12l-3.55-3.55L20.45 12zM12 16.9l-3.55 3.55L12 24l3.55-3.55L12 16.9z" />
        </svg>
      );
    default:
      return null;
  }
}
