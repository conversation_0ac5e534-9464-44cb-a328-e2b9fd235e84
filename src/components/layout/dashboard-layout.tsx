import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { DashboardSidebar } from '@/components/dashboard/dashboard-sidebar';
import { useIsMobile } from '@/hooks/use-mobile';
import { ReactNode } from 'react';

interface DashboardLayoutProps {
  children: ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const isMobile = useIsMobile();

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header - Fixed height */}
      <DashboardHeader isMobile={isMobile} onMenuClick={() => {}}>
        <DashboardSidebar className="w-64 h-full" />
      </DashboardHeader>

      {/* Content area - Takes remaining height */}
      <div className="flex flex-1 overflow-hidden">
        {/* Desktop Sidebar - Fixed width, full height */}
        {!isMobile && (
          <div className="w-64 flex-shrink-0">
            <DashboardSidebar className="w-full h-full" />
          </div>
        )}

        {/* Main Content - Scrollable */}
        <main className="flex-1 overflow-auto">
          <div className="p-4 md:p-6">{children}</div>
        </main>
      </div>
    </div>
  );
}
