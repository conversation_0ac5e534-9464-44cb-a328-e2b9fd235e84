import { supabase } from '@/supabase/supabase';
import {
  InsertTransaction,
  Transaction,
  UpdateTransaction,
} from '@/supabase/types';

export const getTransactions = async (
  userId: string
): Promise<Transaction[]> => {
  const { data, error } = await supabase
    .from('transactions')
    .select('*')
    .eq('user_id', userId);

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const getTransaction = async (
  transactionId: number
): Promise<Transaction | null> => {
  const { data, error } = await supabase
    .from('transactions')
    .select('*')
    .eq('id', transactionId)
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const createTransaction = async (
  transaction: InsertTransaction
): Promise<Transaction> => {
  const { data, error } = await supabase
    .from('transactions')
    .insert(transaction)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const updateTransaction = async (
  transactionId: number,
  transaction: UpdateTransaction
): Promise<Transaction> => {
  const { data, error } = await supabase
    .from('transactions')
    .update(transaction)
    .eq('id', transactionId)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};
