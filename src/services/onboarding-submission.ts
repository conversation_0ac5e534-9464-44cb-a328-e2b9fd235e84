import { createWebsite } from '@/services/website/website-service';
import { InsertWebsite, Website } from '@/supabase/types';
import { OnboardingState } from '@/types/onboarding';

export interface OnboardingSubmissionData {
  website: InsertWebsite;
}

export function transformOnboardingDataForSubmission(
  userId: string,
  onboardingData: OnboardingState
): OnboardingSubmissionData {
  const websiteDomain = onboardingData.website?.domain || '';

  const websiteData: InsertWebsite = {
    user_id: userId,
    domain_name: websiteDomain,
    website_url: websiteDomain ? `https://${websiteDomain}` : '',
    status: 'active',
    wordpress_id: onboardingData.credentials?.username || null,
    wordpress_pass: onboardingData.credentials?.password || null,
  };

  return { website: websiteData };
}

export async function submitOnboardingData(
  userId: string,
  onboardingData: OnboardingState
): Promise<{ website: Website }> {
  const submissionData = transformOnboardingDataForSubmission(
    userId,
    onboardingData
  );

  // Create website
  const website = await createWebsite(submissionData.website);

  return { website };
}
