import { supabase } from '@/supabase/supabase';
import { AuthStepData } from '@/types/onboarding';

export async function signUp(authData: AuthStepData) {
  const { data, error } = await supabase.auth.signUp({
    email: authData.email,
    password: authData.password,
    options: {
      data: {
        full_name: authData.fullName,
      },
    },
  });

  if (error) {
    throw new Error(error.message);
  }

  return data.user;
}
