import { supabase } from '@/supabase/supabase';
import { InsertPlan, Plan, UpdatePlan } from '@/supabase/types';

export const getAllPlans = async (): Promise<Plan[]> => {
  const { data, error } = await supabase
    .from('plans')
    .select('*')
    .order('price', { ascending: true });

  if (error) {
    throw new Error(`Failed to fetch plans: ${error.message}`);
  }

  return data || [];
};

export const getPlanById = async (id: number): Promise<Plan | null> => {
  const { data, error } = await supabase
    .from('plans')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Record not found
    }
    throw new Error(`Failed to fetch plan: ${error.message}`);
  }

  return data;
};

export const createPlan = async (plan: InsertPlan): Promise<Plan> => {
  const { data, error } = await supabase
    .from('plans')
    .insert(plan)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create plan: ${error.message}`);
  }

  return data;
};

export const updatePlan = async (
  id: number,
  updates: UpdatePlan
): Promise<Plan> => {
  const { data, error } = await supabase
    .from('plans')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update plan: ${error.message}`);
  }

  return data;
};

export const deletePlan = async (id: number): Promise<void> => {
  const { error } = await supabase.from('plans').delete().eq('id', id);

  if (error) {
    throw new Error(`Failed to delete plan: ${error.message}`);
  }
};
