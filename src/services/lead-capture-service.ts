import { OnboardingState } from '@/types/onboarding';

const GOOGLE_SHEET_API_URL = import.meta.env.VITE_GOOGLE_SHEET_API_URL || '';

/**
 * Captures lead data and sends it to a Google Sheet via an API.
 * This is intended for users who start but do not complete the onboarding process.
 * @param leadData - The partial or complete onboarding state.
 */
export async function captureLead(leadData: OnboardingState): Promise<void> {
  if (!GOOGLE_SHEET_API_URL) {
    console.log('Google Sheet API URL not configured. Skipping lead capture.');
    // In a real app, you might want to return early or handle this case differently.
    // For this example, we'll log the data instead of sending it.
    console.log('Captured Lead Data:', leadData);
    return;
  }

  try {
    const response = await fetch(GOOGLE_SHEET_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(leadData),
    });

    if (!response.ok) {
      throw new Error(`Failed to capture lead: ${response.statusText}`);
    }

    console.log('Lead captured successfully.');
  } catch (error) {
    console.error('Error capturing lead:', error);
    // Decide if you want to re-throw the error or handle it silently.
  }
}

export const leadCaptureService = {
  captureLead,
};
