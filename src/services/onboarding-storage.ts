import { OnboardingState } from '@/types/onboarding';

const ONBOARDING_STORAGE_KEY = 'seo45_onboarding_data';
const ONBOARDING_COMPLETED_KEY = 'seo45_onboarding_completed';

export function getOnboardingData(): OnboardingState {
  try {
    const stored = localStorage.getItem(ONBOARDING_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch {
    return {};
  }
}

export function setOnboardingData(data: OnboardingState): void {
  try {
    localStorage.setItem(ONBOARDING_STORAGE_KEY, JSON.stringify(data));
  } catch {
    // Silent fail - onboarding data is not critical
  }
}

export function updateOnboardingStep<T>(
  stepKey: keyof OnboardingState,
  stepData: T
): void {
  const currentData = getOnboardingData();
  const updatedData = {
    ...currentData,
    [stepKey]: stepData,
  };
  setOnboardingData(updatedData);
}

export function clearOnboardingData(): void {
  try {
    localStorage.removeItem(ONBOARDING_STORAGE_KEY);
  } catch {
    // Silent fail
  }
}

export function isOnboardingCompleted(): boolean {
  try {
    return localStorage.getItem(ONBOARDING_COMPLETED_KEY) === 'true';
  } catch {
    return false;
  }
}

export function markOnboardingCompleted(): void {
  try {
    localStorage.setItem(ONBOARDING_COMPLETED_KEY, 'true');
  } catch {
    // Silent fail
  }
}
