import { supabase } from '@/supabase/supabase';
import {
  InsertSubscription,
  Plan,
  Subscription,
  UpdateSubscription,
  Website,
} from '@/supabase/types';

export const getAllSubscriptions = async (): Promise<Subscription[]> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .select(
      '*, profiles(id, full_name, email), plans(id, name, price, interval)'
    )
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch subscriptions: ${error.message}`);
  }

  return data || [];
};

export const getSubscriptions = async (
  userId: string
): Promise<Subscription[]> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*, plans(*)')
    .eq('user_id', userId);

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const getSubscription = async (
  subscriptionId: number
): Promise<Subscription | null> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*, plans(*)')
    .eq('id', subscriptionId)
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const createSubscription = async (
  subscription: InsertSubscription
): Promise<Subscription> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .insert(subscription)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const updateSubscription = async (
  subscriptionId: number,
  subscription: UpdateSubscription
): Promise<Subscription> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .update(subscription)
    .eq('id', subscriptionId)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const deleteSubscription = async (subscriptionId: number) => {
  const { data, error } = await supabase
    .from('subscriptions')
    .delete()
    .eq('id', subscriptionId);

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const getPlans = async (): Promise<Plan[]> => {
  const { data, error } = await supabase.from('plans').select('*');

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const getPlan = async (planId: number): Promise<Plan | null> => {
  const { data, error } = await supabase
    .from('plans')
    .select('*')
    .eq('id', planId)
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const getUserSubscriptionWithPlan = async (
  userId: string
): Promise<(Subscription & { plans: Plan }) | null> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*, plans(*)')
    .eq('user_id', userId)
    .eq('status', 'active')
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // No active subscription found
    }
    throw new Error(error.message);
  }

  return data;
};

export const getWebsitesWithSubscriptions = async (
  userId: string
): Promise<(Website & { subscription?: Subscription & { plans: Plan } })[]> => {
  // Get user's websites
  const { data: websites, error: websitesError } = await supabase
    .from('websites')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (websitesError) {
    throw new Error(`Failed to fetch websites: ${websitesError.message}`);
  }

  // Get user's subscription with plan details
  const subscription = await getUserSubscriptionWithPlan(userId);

  // Add subscription to all websites (since subscription is per user, not per website)
  return (websites || []).map(website => ({
    ...website,
    subscription: subscription || undefined,
  }));
};

export const cancelSubscription = async (
  subscriptionId: number
): Promise<Subscription> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .update({
      cancel_at_period_end: true,
      updated_at: new Date().toISOString(),
    })
    .eq('id', subscriptionId)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to cancel subscription: ${error.message}`);
  }

  return data;
};

export const reactivateSubscription = async (
  subscriptionId: number
): Promise<Subscription> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .update({
      cancel_at_period_end: false,
      canceled_at: null,
      updated_at: new Date().toISOString(),
    })
    .eq('id', subscriptionId)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to reactivate subscription: ${error.message}`);
  }

  return data;
};

export const getActiveSubscriptionByUserId = async (
  userId: string
): Promise<(Subscription & { plans: Plan }) | null> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*, plans(*)')
    .eq('user_id', userId)
    .in('status', ['active', 'trialing'])
    .order('created_at', { ascending: false })
    .limit(1)
    .maybeSingle();

  if (error) {
    throw new Error(`Failed to fetch active subscription: ${error.message}`);
  }

  return data;
};

export const getSubscriptionHistory = async (
  userId: string
): Promise<(Subscription & { plans: Plan })[]> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*, plans(*)')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch subscription history: ${error.message}`);
  }

  return data || [];
};

export const upgradeSubscription = async (
  subscriptionId: number,
  newPlanId: number
): Promise<Subscription> => {
  const { data, error } = await supabase
    .from('subscriptions')
    .update({
      plan_id: newPlanId,
      updated_at: new Date().toISOString(),
    })
    .eq('id', subscriptionId)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to upgrade subscription: ${error.message}`);
  }

  return data;
};

export const isSubscriptionActive = (subscription?: Subscription): boolean => {
  if (!subscription) return false;

  const now = new Date();
  const periodEnd = new Date(subscription.current_period_end);

  return (
    subscription.status === 'active' &&
    periodEnd > now &&
    !subscription.cancel_at_period_end
  );
};

export const getSubscriptionStatusDetails = (subscription?: Subscription) => {
  if (!subscription) {
    return {
      status: 'inactive',
      statusText: 'No subscription',
      statusColor: 'secondary' as const,
      isActive: false,
      canUpgrade: true,
      canCancel: false,
      canReactivate: false,
    };
  }

  const now = new Date();
  const periodEnd = new Date(subscription.current_period_end);
  const isExpired = periodEnd <= now;
  const isActive = isSubscriptionActive(subscription);

  let status = subscription.status;
  let statusText = subscription.status;
  let statusColor: 'default' | 'secondary' | 'destructive' | 'outline' =
    'secondary';

  if (subscription.cancel_at_period_end) {
    status = 'ending';
    statusText = 'Ending Soon';
    statusColor = 'destructive';
  } else if (isExpired && subscription.status === 'active') {
    status = 'expired';
    statusText = 'Expired';
    statusColor = 'destructive';
  } else if (subscription.status === 'active') {
    statusText = 'Active';
    statusColor = 'default';
  } else if (subscription.status === 'trialing') {
    statusText = 'Trial';
    statusColor = 'secondary';
  }

  return {
    status,
    statusText,
    statusColor,
    isActive,
    canUpgrade: isActive,
    canCancel: isActive && !subscription.cancel_at_period_end,
    canReactivate: subscription.cancel_at_period_end && !isExpired,
  };
};
