import {
  createC<PERSON>pon,
  deleteCoupon,
  getAll<PERSON><PERSON>pons,
  getCouponById,
  updateCoupon,
} from '@/services/coupon/coupon-service';
import {
  createPlan,
  deletePlan,
  getAllPlans,
  getPlanById,
  updatePlan,
} from '@/services/plan/plan-service';
import {
  createProfile,
  deleteProfile,
  getAllProfiles,
  getProfile,
  updateProfile,
} from '@/services/profile/profile-service';
import {
  createSubscription,
  deleteSubscription,
  getAllSubscriptions,
  getSubscription,
  updateSubscription,
} from '@/services/subscription/subscription-service';
import { supabase } from '@/supabase/supabase';
import { InsertProfile } from '@/supabase/types';
import { AdminUserService } from './user-management-service';

// Create a complete user (auth + profile)
const createCompleteUser = async (
  userData: InsertProfile & { password?: string }
) => {
  const { password = 'TempPassword123!', ...profileData } = userData;

  if (!profileData.email) {
    throw new Error('Email is required to create a user');
  }

  // Create auth user first
  const { data: authData, error: authError } =
    await supabase.auth.admin.createUser({
      email: profileData.email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: profileData.full_name,
      },
    });

  if (authError) {
    throw new Error(`Failed to create auth user: ${authError.message}`);
  }

  // Create profile with auth user's ID
  const insertData: InsertProfile = {
    ...profileData,
    id: authData.user.id,
  };

  return await createProfile(insertData);
};

// Delete a complete user (auth + profile)
const deleteCompleteUser = async (userId: string) => {
  // Delete profile first
  await deleteProfile(userId);

  // Then delete auth user
  const { error: authError } = await supabase.auth.admin.deleteUser(userId);

  if (authError) {
    // Log the error but don't throw since profile is already deleted
    console.warn(`Failed to delete auth user ${userId}: ${authError.message}`);
  }
};

// Re-export all admin-related services
export {
  createCompleteUser,
  createCoupon,
  createPlan,
  createProfile,
  createSubscription,
  deleteCompleteUser,
  deleteCoupon,
  deletePlan,
  deleteProfile,
  deleteSubscription,
  // Coupon operations
  getAllCoupons,
  // Plan operations
  getAllPlans,
  // Profile operations
  getAllProfiles,
  // Subscription operations
  getAllSubscriptions,
  getCouponById,
  getPlanById,
  getProfile,
  getSubscription,
  updateCoupon,
  updatePlan,
  updateProfile,
  updateSubscription,
};

// For backward compatibility, export the admin service as an object with methods
export const adminService = {
  // Coupon operations
  getAllCoupons,
  getCouponById,
  createCoupon,
  updateCoupon,
  deleteCoupon,

  // Plan operations
  getAllPlans,
  getPlanById,
  createPlan,
  updatePlan,
  deletePlan,

  // Profile operations (users)
  getAllUsers: getAllProfiles,
  getUserById: getProfile,
  createUser: createCompleteUser, // Use the new complete user creation
  updateUser: updateProfile,
  deleteUser: deleteCompleteUser, // Use complete user deletion

  // Subscription operations
  getAllSubscriptions,
  getSubscriptionById: getSubscription,
  createSubscription,
  updateSubscription,
  deleteSubscription,

  // Enhanced user management
  getAllUsersWithDetails: AdminUserService.getAllUsersWithDetails,
  getUserWithDetails: AdminUserService.getUserWithDetails,
  createCompleteUser: AdminUserService.createCompleteUser,
  updateUserProfile: AdminUserService.updateUserProfile,
  deleteCompleteUser: AdminUserService.deleteCompleteUser,
  resetUserPassword: AdminUserService.resetUserPassword,
  toggleUserStatus: AdminUserService.toggleUserStatus,
  getUserSubscriptionSummary: AdminUserService.getUserSubscriptionSummary,
};
