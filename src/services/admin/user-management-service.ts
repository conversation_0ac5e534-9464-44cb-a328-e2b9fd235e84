import { supabase } from '@/supabase/supabase';
import { InsertProfile, Profile } from '@/supabase/types';
import {
  CreateUserData,
  UpdateUserData,
  UserFilters,
  UserSort,
  UserWithAuthData,
} from '@/types/admin/user-management';

// Enhanced admin service for user management
export class AdminUserService {
  // Get all users with auth data and subscription info
  static async getAllUsersWithDetails(
    filters?: UserFilters,
    sort?: UserSort,
    page = 1,
    limit = 50
  ): Promise<{ users: UserWithAuthData[]; total: number }> {
    try {
      let query = supabase.from('profiles').select(
        `
          *,
          subscriptions:subscriptions(
            id,
            status,
            current_period_start,
            current_period_end,
            cancel_at_period_end,
            created_at,
            plans:plans(
              id,
              name,
              price,
              interval
            )
          )
        `,
        { count: 'exact' }
      );

      // Apply filters
      if (filters?.role) {
        query = query.eq('role', filters.role);
      }

      if (filters?.search) {
        query = query.or(
          `full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,username.ilike.%${filters.search}%`
        );
      }

      // Apply sorting
      if (sort) {
        query = query.order(sort.field, {
          ascending: sort.direction === 'asc',
        });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data: profiles, error, count } = await query;

      if (error) {
        throw new Error(`Failed to fetch users: ${error.message}`);
      }

      // Get auth user data for each profile
      const usersWithAuthData: UserWithAuthData[] = [];

      for (const profile of profiles || []) {
        try {
          // Get auth user data (only for service role access)
          const { data: authUser } = await supabase.auth.admin.getUserById(
            profile.id
          );

          usersWithAuthData.push({
            ...profile,
            auth_user: authUser.user
              ? {
                  id: authUser.user.id,
                  email: authUser.user.email || '',
                  email_confirmed_at: authUser.user.email_confirmed_at,
                  last_sign_in_at: authUser.user.last_sign_in_at,
                  created_at: authUser.user.created_at,
                  updated_at: authUser.user.updated_at,
                  user_metadata: authUser.user.user_metadata || {},
                }
              : undefined,
            subscription_count: profile.subscriptions?.length || 0,
            last_login: authUser.user?.last_sign_in_at || null,
          });
        } catch {
          // Include profile even if auth data fetch fails
          usersWithAuthData.push({
            ...profile,
            subscription_count: profile.subscriptions?.length || 0,
          });
        }
      }

      return {
        users: usersWithAuthData,
        total: count || 0,
      };
    } catch (error) {
      console.error('Error fetching users with details:', error);
      throw error;
    }
  }

  // Get single user with full details
  static async getUserWithDetails(
    userId: string
  ): Promise<UserWithAuthData | null> {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select(
          `
          *,
          subscriptions:subscriptions(
            id,
            status,
            current_period_start,
            current_period_end,
            cancel_at_period_end,
            created_at,
            updated_at,
            plans:plans(*)
          )
        `
        )
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw new Error(`Failed to fetch user: ${error.message}`);
      }

      // Get auth user data
      const { data: authUser } = await supabase.auth.admin.getUserById(userId);

      return {
        ...profile,
        auth_user: authUser.user
          ? {
              id: authUser.user.id,
              email: authUser.user.email || '',
              email_confirmed_at: authUser.user.email_confirmed_at,
              last_sign_in_at: authUser.user.last_sign_in_at,
              created_at: authUser.user.created_at,
              updated_at: authUser.user.updated_at,
              user_metadata: authUser.user.user_metadata || {},
            }
          : undefined,
        subscription_count: profile.subscriptions?.length || 0,
        last_login: authUser.user?.last_sign_in_at || null,
      };
    } catch (error) {
      console.error('Error fetching user details:', error);
      throw error;
    }
  }

  // Create complete user (auth + profile)
  static async createCompleteUser(
    userData: CreateUserData
  ): Promise<UserWithAuthData> {
    const {
      password = 'TempPassword123!',
      send_email_confirmation = true,
      ...profileData
    } = userData;

    if (!profileData.email) {
      throw new Error('Email is required to create a user');
    }

    try {
      // Create auth user first
      const { data: authData, error: authError } =
        await supabase.auth.admin.createUser({
          email: profileData.email,
          password,
          email_confirm: send_email_confirmation,
          user_metadata: {
            full_name: profileData.full_name,
          },
        });

      if (authError) {
        throw new Error(`Failed to create auth user: ${authError.message}`);
      }

      // Create profile with auth user's ID
      const insertData: InsertProfile = {
        ...profileData,
        id: authData.user.id,
      };

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .insert(insertData)
        .select()
        .single();

      if (profileError) {
        // Try to clean up auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        throw new Error(`Failed to create profile: ${profileError.message}`);
      }

      return {
        ...profile,
        auth_user: {
          id: authData.user.id,
          email: authData.user.email || '',
          email_confirmed_at: authData.user.email_confirmed_at,
          last_sign_in_at: authData.user.last_sign_in_at,
          created_at: authData.user.created_at,
          updated_at: authData.user.updated_at,
          user_metadata: authData.user.user_metadata || {},
        },
        subscription_count: 0,
        last_login: null,
      };
    } catch (error) {
      console.error('Error creating complete user:', error);
      throw error;
    }
  }

  // Update user profile
  static async updateUserProfile(
    userId: string,
    userData: UpdateUserData
  ): Promise<Profile> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(userData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update profile: ${error.message}`);
      }

      // If email is being updated, update auth user too
      if (userData.email) {
        const { error: authError } = await supabase.auth.admin.updateUserById(
          userId,
          {
            email: userData.email,
          }
        );

        if (authError) {
          console.warn(`Failed to update auth email: ${authError.message}`);
        }
      }

      return data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Delete complete user (auth + profile + related data)
  static async deleteCompleteUser(userId: string): Promise<void> {
    try {
      // Delete related data first (subscriptions, etc.)
      await supabase.from('subscriptions').delete().eq('user_id', userId);
      await supabase.from('websites').delete().eq('user_id', userId);

      // Delete profile
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) {
        throw new Error(`Failed to delete profile: ${profileError.message}`);
      }

      // Delete auth user
      const { error: authError } = await supabase.auth.admin.deleteUser(userId);

      if (authError) {
        console.warn(
          `Failed to delete auth user ${userId}: ${authError.message}`
        );
      }
    } catch (error) {
      console.error('Error deleting complete user:', error);
      throw error;
    }
  }

  // Reset user password
  static async resetUserPassword(
    userId: string,
    newPassword: string
  ): Promise<void> {
    try {
      const { error } = await supabase.auth.admin.updateUserById(userId, {
        password: newPassword,
      });

      if (error) {
        throw new Error(`Failed to reset password: ${error.message}`);
      }
    } catch (error) {
      console.error('Error resetting user password:', error);
      throw error;
    }
  }

  // Suspend/Activate user
  static async toggleUserStatus(
    userId: string,
    banned: boolean
  ): Promise<void> {
    try {
      const { error } = await supabase.auth.admin.updateUserById(userId, {
        ban_duration: banned ? 'indefinite' : 'none',
      });

      if (error) {
        throw new Error(`Failed to update user status: ${error.message}`);
      }
    } catch (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }
  }

  // Get user's subscription summary
  static async getUserSubscriptionSummary(userId: string) {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select(
          `
          id,
          status,
          current_period_start,
          current_period_end,
          cancel_at_period_end,
          created_at,
          plans:plans(
            id,
            name,
            price,
            interval
          )
        `
        )
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(
          `Failed to fetch subscription summary: ${error.message}`
        );
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching subscription summary:', error);
      throw error;
    }
  }
}
