import { supabase } from '@/supabase/supabase';
import { Profile } from '@/supabase/types';
import {
  Article,
  ArticleStats,
  CreateArticleParams,
  FetchArticlesParams,
  UpdateArticleParams,
} from '@/types/article';

// Helper function to get user's Apps Script URL from profile
const getUserAppsScriptUrl = async (userId: string): Promise<string | null> => {
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error || !profile) {
    throw new Error('Failed to get user profile');
  }

  // Try to get apps_script_url from profile data
  // Note: This assumes the field exists in the database schema
  // For now, we'll use website field as fallback
  const extendedProfile = profile as Profile & {
    apps_script_url?: string | null;
  };
  const appsScriptUrl = extendedProfile.apps_script_url || profile.website;

  return appsScriptUrl || null;
};

// Fetch articles from Google Apps Script
export const getArticles = async (
  userId: string,
  params: FetchArticlesParams = {}
): Promise<Article[]> => {
  try {
    const appsScriptUrl = await getUserAppsScriptUrl(userId);

    if (!appsScriptUrl) {
      console.warn('No Apps Script URL found for user');
      return [];
    }

    const queryParams = new URLSearchParams();
    if (params.website_id) queryParams.append('website_id', params.website_id);
    if (params.status) queryParams.append('status', params.status);
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());

    const url = `${appsScriptUrl}?action=getArticles&${queryParams.toString()}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Apps Script API error: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    return data.articles || [];
  } catch (error) {
    console.error('Error fetching articles:', error);
    throw error;
  }
};

// Get articles for a specific website
export const getArticlesByWebsite = async (
  userId: string,
  websiteId: string
): Promise<Article[]> => {
  return getArticles(userId, { website_id: websiteId });
};

// Get article statistics
export const getArticleStats = async (
  userId: string
): Promise<ArticleStats> => {
  try {
    const articles = await getArticles(userId);

    const stats: ArticleStats = {
      total_articles: articles.length,
      published_articles: articles.filter(a => a.status === 'published').length,
      draft_articles: articles.filter(a => a.status === 'draft').length,
      total_views: articles.reduce(
        (sum, article) => sum + (article.views || 0),
        0
      ),
      avg_seo_score:
        articles.length > 0
          ? articles.reduce(
              (sum, article) => sum + (article.seo_score || 0),
              0
            ) / articles.length
          : 0,
    };

    return stats;
  } catch (error) {
    console.error('Error getting article stats:', error);
    throw error;
  }
};

// Create a new article
export const createArticle = async (
  userId: string,
  articleData: CreateArticleParams
): Promise<Article> => {
  try {
    const appsScriptUrl = await getUserAppsScriptUrl(userId);

    if (!appsScriptUrl) {
      throw new Error('No Apps Script URL configured for user');
    }

    const response = await fetch(appsScriptUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'createArticle',
        ...articleData,
      }),
    });

    if (!response.ok) {
      throw new Error(`Apps Script API error: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    return data.article;
  } catch (error) {
    console.error('Error creating article:', error);
    throw error;
  }
};

// Update an article
export const updateArticle = async (
  userId: string,
  articleId: string,
  updateData: UpdateArticleParams
): Promise<Article> => {
  try {
    const appsScriptUrl = await getUserAppsScriptUrl(userId);

    if (!appsScriptUrl) {
      throw new Error('No Apps Script URL configured for user');
    }

    const response = await fetch(appsScriptUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'updateArticle',
        articleId,
        ...updateData,
      }),
    });

    if (!response.ok) {
      throw new Error(`Apps Script API error: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    return data.article;
  } catch (error) {
    console.error('Error updating article:', error);
    throw error;
  }
};

// Delete an article
export const deleteArticle = async (
  userId: string,
  articleId: string
): Promise<void> => {
  try {
    const appsScriptUrl = await getUserAppsScriptUrl(userId);

    if (!appsScriptUrl) {
      throw new Error('No Apps Script URL configured for user');
    }

    const response = await fetch(appsScriptUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'deleteArticle',
        articleId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Apps Script API error: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error('Error deleting article:', error);
    throw error;
  }
};
