import { supabase } from '@/supabase/supabase';
import { InsertWebsite, UpdateWebsite, Website } from '@/supabase/types';

export const getWebsites = async (userId: string): Promise<Website[]> => {
  const { data, error } = await supabase
    .from('websites')
    .select('*')
    .eq('user_id', userId);

  if (error) {
    throw new Error(error.message);
  }

  return data || [];
};

export const getWebsitesByUser = async (userId: string): Promise<Website[]> => {
  const { data, error } = await supabase
    .from('websites')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to get websites: ${error.message}`);
  }

  return data || [];
};

export const getWebsiteByDomain = async (
  domain: string
): Promise<Website | null> => {
  const { data, error } = await supabase
    .from('websites')
    .select('*')
    .eq('domain', domain)
    .single();

  if (error && error.code !== 'PGRST116') {
    throw new Error(`Failed to get website: ${error.message}`);
  }

  return data;
};

export const getWebsite = async (
  websiteId: number
): Promise<Website | null> => {
  const { data, error } = await supabase
    .from('websites')
    .select('*')
    .eq('id', websiteId)
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const createWebsite = async (
  website: InsertWebsite
): Promise<Website> => {
  const { data, error } = await supabase
    .from('websites')
    .insert(website)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create website: ${error.message}`);
  }

  return data;
};

export const updateWebsite = async (
  websiteId: number | string,
  website: UpdateWebsite
): Promise<Website> => {
  const { data, error } = await supabase
    .from('websites')
    .update(website)
    .eq('id', websiteId)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update website: ${error.message}`);
  }

  return data;
};

export const deleteWebsite = async (
  websiteId: number | string
): Promise<void> => {
  const { error } = await supabase
    .from('websites')
    .delete()
    .eq('id', websiteId);

  if (error) {
    throw new Error(`Failed to delete website: ${error.message}`);
  }
};
