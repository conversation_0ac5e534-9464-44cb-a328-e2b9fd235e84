import { supabase } from '@/supabase/supabase';
import { Coupon, InsertCoupon, UpdateCoupon } from '@/supabase/types';

export const getAllCoupons = async (): Promise<Coupon[]> => {
  const { data, error } = await supabase
    .from('coupons')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch coupons: ${error.message}`);
  }

  return data || [];
};

export const getCouponById = async (id: number): Promise<Coupon | null> => {
  const { data, error } = await supabase
    .from('coupons')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return null; // Record not found
    }
    throw new Error(`Failed to fetch coupon: ${error.message}`);
  }

  return data;
};

export const getCoupons = async (): Promise<Coupon[]> => {
  const { data, error } = await supabase.from('coupons').select('*');

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const getCouponByCode = async (code: string): Promise<Coupon | null> => {
  const { data, error } = await supabase
    .from('coupons')
    .select('*')
    .eq('code', code)
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const createCoupon = async (coupon: InsertCoupon): Promise<Coupon> => {
  const { data, error } = await supabase
    .from('coupons')
    .insert(coupon)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const updateCoupon = async (
  couponId: number,
  coupon: UpdateCoupon
): Promise<Coupon> => {
  const { data, error } = await supabase
    .from('coupons')
    .update(coupon)
    .eq('id', couponId)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const deleteCoupon = async (id: number): Promise<void> => {
  const { error } = await supabase.from('coupons').delete().eq('id', id);

  if (error) {
    throw new Error(`Failed to delete coupon: ${error.message}`);
  }
};
