import { OnboardingState } from '@/types/onboarding';

const GOOGLE_SCRIPT_URL = import.meta.env.VITE_GOOGLE_SHEET_API_URL || '';

/**
 * Saves the user's onboarding data to a Google Sheet via a Google Apps Script.
 * This is used to persist onboarding progress for users who may not complete the process
 * in a single session or who haven't created an account yet.
 * @param onboardingData - The partial or complete onboarding state.
 */
export async function saveOnboardingData(
  onboardingData: OnboardingState
): Promise<void> {
  if (!GOOGLE_SCRIPT_URL) {
    console.log('Google Apps Script URL not configured. Skipping data save.');
    // Log data locally for development purposes
    console.log('Onboarding Data:', onboardingData);
    return;
  }

  // Debug: Log the data being sent
  console.log('Sending onboarding data to Google Apps Script:', {
    url: GOOGLE_SCRIPT_URL,
    data: onboardingData,
    dataString: JSON.stringify(onboardingData),
  });

  try {
    const response = await fetch(GOOGLE_SCRIPT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(onboardingData),
    });

    const responseText = await response.text();
    console.log('Google Apps Script response:', {
      status: response.status,
      statusText: response.statusText,
      body: responseText,
    });

    if (!response.ok) {
      throw new Error(
        `Failed to save onboarding data: ${response.statusText} - ${responseText}`
      );
    }

    console.log('Onboarding data saved successfully.');
  } catch (error) {
    console.error('Error saving onboarding data:', error);
  }
}
